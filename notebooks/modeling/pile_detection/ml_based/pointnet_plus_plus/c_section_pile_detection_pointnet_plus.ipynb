{
 "cells": [
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "427d2937",
   "metadata": {},
   "outputs": [],
   "source": [
    "# SECTION 1: Imports & Parameters\n",
    "import numpy as np\n",
    "import pandas as pd\n",
    "import open3d as o3d\n",
    "from pathlib import Path\n",
    "import matplotlib.pyplot as plt\n",
    "from sklearn.cluster import DBSCAN\n",
    "from sklearn.neighbors import NearestNeighbors\n",
    "from sklearn.decomposition import PCA\n",
    "import json\n",
    "from datetime import datetime\n",
    "import tensorflow as tf\n"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "3dfc05fe",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Parameters\n",
    "site_name = \"trino_enel\"\n",
    "ground_method = \"csf\"\n",
    "confidence_threshold = 0.5  # LOWERED from 0.6 or 0.7\n",
    "output_dir = Path(\"../../../../../data/output_runs/pile_detection\") / ground_method\n",
    "output_dir.mkdir(parents=True, exist_ok=True)\n",
    "print(f\"Saving results to: {output_dir}\")\n",
    "\n",
    "point_cloud_file = Path(f\"../../../../../data/output_runs/icp_alignment_corrected/{ground_method}/aligned_ifc_{ground_method}.ply\")\n",
    "\n",
    "# Setup\n",
    "output_path = Path(output_dir) / ground_method\n",
    "output_path.mkdir(parents=True, exist_ok=True)\n",
    "detections_file = output_path / f\"c_section_detections_{ground_method}.csv\"\n",
    "print(f\"Saving detections to: {detections_file}\")\n"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "d7ae0b0a",
   "metadata": {},
   "outputs": [],
   "source": [
    "# === 2. LOAD POINT CLOUD ===\n",
    "print(\"Loading point cloud...\")\n",
    "if not point_cloud_file.exists():\n",
    "    point_cloud_file = Path(f\"../../../../../data/ground_segmentation/{ground_method}/ifc_ground.ply\")\n",
    "\n",
    "pcd = o3d.io.read_point_cloud(str(point_cloud_file))\n",
    "points = np.asarray(pcd.points)\n",
    "\n",
    "print(f\"Loaded {points.shape[0]:,} points from {point_cloud_file}\")\n"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "04a7a085",
   "metadata": {},
   "outputs": [],
   "source": [
    "# === 3. ENHANCED CONFIGURATION ===\n",
    "\n",
    "import pandas as pd\n",
    "from pathlib import Path\n",
    "import numpy as np\n",
    "from sklearn.neighbors import NearestNeighbors\n",
    "from sklearn.model_selection import train_test_split\n",
    "import json\n",
    "\n",
    "class CSectionPileConfig:\n",
    "    \"\"\"Enhanced configuration for C-section pile detection with IFC ground truth\"\"\"\n",
    "    \n",
    "    def __init__(self):\n",
    "        # === IFC Ground Truth Data ===\n",
    "        self.ifc_pile_csv = \"data/processed/trino_enel/advanced_ifc_metadata/advanced_tracker_piles.csv\"\n",
    "        self.expected_pile_count = 14460  # Ground truth from IFC\n",
    "        \n",
    "        # === C-section Physical Dimensions ===\n",
    "        # Based on TRJHT56PDP-BF model specifications\n",
    "        self.flange_width_range = (0.05, 0.3)      # meters\n",
    "        self.web_height_range = (0.1, 0.8)         # meters  \n",
    "        self.thickness_range = (0.005, 0.03)       # meters\n",
    "        self.opening_width_range = (0.08, 0.25)    # meters\n",
    "        self.pile_height_range = (1.0, 5.0)        # meters above ground\n",
    "        \n",
    "        # === Patch Extraction Parameters ===\n",
    "        self.patch_radius = 1.5                    # meters - spatial radius for patch extraction\n",
    "        self.num_points_per_patch = 512             # consistent point count per patch\n",
    "        self.min_points_per_patch = 128             # minimum points to consider valid patch\n",
    "        self.overlap_ratio = 0.3                   # overlap between patches for augmentation\n",
    "        \n",
    "        # === Training Data Parameters ===\n",
    "        self.positive_sample_ratio = 0.7           # ratio of IFC piles to use as positives\n",
    "        self.negative_sample_multiplier = 2.0      # negatives per positive sample\n",
    "        self.validation_split = 0.2                # validation data split\n",
    "        self.test_split = 0.1                      # test data split\n",
    "        \n",
    "        # === Model Architecture Parameters ===\n",
    "        self.num_classes = 2                       # pile vs non-pile\n",
    "        self.dropout_rate = 0.3                    # dropout for regularization\n",
    "        self.learning_rate = 0.001                 # initial learning rate\n",
    "        self.batch_size = 16                       # training batch size\n",
    "        self.epochs = 50                           # training epochs\n",
    "        \n",
    "        # === Geometric Detection (Legacy) ===\n",
    "        self.min_height = 0.8                      # minimum Z elevation from ground\n",
    "        self.expected_width = 0.15                 # expected pile width\n",
    "        self.expected_depth = 0.08                 # expected pile depth  \n",
    "        self.width_tolerance = 0.1                 # allowed variation in width\n",
    "        self.min_points_per_cluster = 50           # minimum points in cluster\n",
    "        self.confidence_threshold = 0.7            # minimum score for detection\n",
    "        self.dbscan_eps = 0.25                     # radius for clustering\n",
    "        self.max_cluster_size = 500                # ignore overly large clusters\n",
    "        \n",
    "        # === Coordinate System ===\n",
    "        self.utm_zone = \"32N\"                      # UTM Zone 32N (EPSG:32632)\n",
    "        self.coordinate_bounds = {\n",
    "            'x_min': 435267, 'x_max': 436720,      # UTM X bounds\n",
    "            'y_min': 5010901, 'y_max': 5012462,    # UTM Y bounds  \n",
    "            'z_min': 157.1, 'z_max': 161.7         # elevation bounds\n",
    "        }\n",
    "        \n",
    "        # === Output Configuration ===\n",
    "        self.save_patches = True                    # save extracted patches for analysis\n",
    "        self.save_model = True                      # save trained model\n",
    "        self.mlflow_tracking = True                 # enable MLflow experiment tracking\n",
    "        self.visualization = True                   # enable result visualization\n",
    "\n",
    "# Initialize configuration\n",
    "config = CSectionPileConfig()\n",
    "print(f\"Configuration initialized:\")\n",
    "print(f\"  Expected piles: {config.expected_pile_count}\")\n",
    "print(f\"  IFC data source: {config.ifc_pile_csv}\")\n",
    "print(f\"  Patch radius: {config.patch_radius}m\")\n",
    "print(f\"  Points per patch: {config.num_points_per_patch}\")\n",
    "print(f\"  Training epochs: {config.epochs}\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "load_ground_truth",
   "metadata": {},
   "outputs": [],
   "source": [
    "# === 4. LOAD IFC GROUND TRUTH DATA ===\n",
    "\n",
    "def load_ifc_ground_truth(config):\n",
    "    \"\"\"Load IFC pile coordinates as ground truth data\"\"\"\n",
    "    try:\n",
    "        # Load IFC pile data\n",
    "        ifc_piles = pd.read_csv(config.ifc_pile_csv)\n",
    "        print(f\"Loaded {len(ifc_piles)} IFC pile records\")\n",
    "        \n",
    "        # Extract coordinates\n",
    "        pile_coords = ifc_piles[['X', 'Y', 'Z']].values\n",
    "        pile_ids = ifc_piles['Tag'].values\n",
    "        pile_names = ifc_piles['Name'].values\n",
    "        \n",
    "        # Validate coordinate bounds\n",
    "        bounds = config.coordinate_bounds\n",
    "        valid_mask = (\n",
    "            (pile_coords[:, 0] >= bounds['x_min']) & (pile_coords[:, 0] <= bounds['x_max']) &\n",
    "            (pile_coords[:, 1] >= bounds['y_min']) & (pile_coords[:, 1] <= bounds['y_max']) &\n",
    "            (pile_coords[:, 2] >= bounds['z_min']) & (pile_coords[:, 2] <= bounds['z_max'])\n",
    "        )\n",
    "        \n",
    "        valid_coords = pile_coords[valid_mask]\n",
    "        valid_ids = pile_ids[valid_mask]\n",
    "        valid_names = pile_names[valid_mask]\n",
    "        \n",
    "        print(f\"Valid piles within bounds: {len(valid_coords)}\")\n",
    "        print(f\"Coordinate ranges:\")\n",
    "        print(f\"  X: {valid_coords[:, 0].min():.1f} - {valid_coords[:, 0].max():.1f}\")\n",
    "        print(f\"  Y: {valid_coords[:, 1].min():.1f} - {valid_coords[:, 1].max():.1f}\")\n",
    "        print(f\"  Z: {valid_coords[:, 2].min():.1f} - {valid_coords[:, 2].max():.1f}\")\n",
    "        \n",
    "        return {\n",
    "            'coordinates': valid_coords,\n",
    "            'ids': valid_ids,\n",
    "            'names': valid_names,\n",
    "            'count': len(valid_coords)\n",
    "        }\n",
    "        \n",
    "    except Exception as e:\n",
    "        print(f\"Error loading IFC ground truth: {e}\")\n",
    "        return None\n",
    "\n",
    "# Load ground truth data\n",
    "ground_truth = load_ifc_ground_truth(config)\n",
    "if ground_truth:\n",
    "    print(f\"\\nGround truth loaded successfully!\")\n",
    "    print(f\"Total valid piles: {ground_truth['count']}\")\n",
    "else:\n",
    "    print(\"Failed to load ground truth data\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "6801c4ba",
   "metadata": {},
   "outputs": [],
   "source": [
    "# === GEOMETRIC DETECTION INTEGRATION ===\n",
    "# Note: Geometric detection has been moved to:\n",
    "# notebooks/modeling/pile_detection/rule_based/01_geometric_pile_detection.ipynb\n",
    "\n",
    "def load_geometric_detections(config):\n",
    "    \"\"\"Load geometric detections from the dedicated rule-based notebook output\"\"\"\n",
    "    geometric_results_file = Path(f\"../../../../../data/output_runs/pile_detection/geometric_{config.ground_method}/geometric_detections_{config.ground_method}.csv\")\n",
    "    \n",
    "    if geometric_results_file.exists():\n",
    "        geo_df = pd.read_csv(geometric_results_file)\n",
    "        detections = []\n",
    "        for _, row in geo_df.iterrows():\n",
    "            detections.append({\n",
    "                'x': row['x'],\n",
    "                'y': row['y'],\n",
    "                'z': row['z'],\n",
    "                'confidence': row['confidence'],\n",
    "                'point_count': row['point_count'],\n",
    "                'source': 'geometric_rule_based'\n",
    "            })\n",
    "        print(f\"Loaded {len(detections)} geometric detections from {geometric_results_file}\")\n",
    "        return detections\n",
    "    else:\n",
    "        print(f\"Geometric detections file not found: {geometric_results_file}\")\n",
    "        print(\"Run the geometric detection notebook first: rule_based/01_geometric_pile_detection.ipynb\")\n",
    "        return []\n",
    "\n",
    "# Define constants for compatibility\n",
    "confidence_threshold = config.confidence_threshold"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "8d44e8be",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Geometric detection functions have been moved to:\n",
    "# notebooks/modeling/pile_detection/rule_based/01_geometric_pile_detection.ipynb"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "e4d24230",
   "metadata": {},
   "outputs": [],
   "source": [
# Geometric detection functions moved to rule_based/01_geometric_pile_detection.ipynb
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "94fe66d7",
   "metadata": {},
   "outputs": [],
   "source": [
    "def analyze_cluster(cluster):\n",
    "    # Step 3: Analyze a given cluster’s shape and point distribution\n",
    "    x_span = np.ptp(cluster[:, 0])\n",
    "    y_span = np.ptp(cluster[:, 1])\n",
    "    z_span = np.ptp(cluster[:, 2])\n",
    "\n",
    "    # Score based on how tall the structure is\n",
    "    h_score = min(z_span / MIN_HEIGHT, 1.0)\n",
    "    max_h = max(x_span, y_span)\n",
    "    min_h = min(x_span, y_span)\n",
    "    \n",
    "    # Check if the widest horizontal side matches expected width\n",
    "    width_score = 0.7 if (EXPECTED_WIDTH - WIDTH_TOLERANCE <= max_h <= EXPECTED_WIDTH + WIDTH_TOLERANCE) else 0.0\n",
    "    aspect = max_h / (min_h + 1e-6)\n",
    "    aspect_score = 0.8 if 1.5 <= aspect <= 4.0 else 0.0\n",
    "    dist_score = calculate_distribution_score(cluster)\n",
    "\n",
    "    # Score based on aspect ratio (width-to-depth ratio)\n",
    "    confidence = h_score * 0.3 + width_score * 0.3 + aspect_score * 0.2 + dist_score * 0.2\n",
    "    print(f\"Cluster analysis: h={h_score:.2f}, w={width_score:.2f}, a={aspect_score:.2f}, d={dist_score:.2f}\")\n",
    "\n",
    "    return confidence, z_span, max_h, min_h, len(cluster), dist_score"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "52c77564",
   "metadata": {},
   "outputs": [],
   "source": [
    "def compute_center(cluster):\n",
    "    # Compute the center position of the structure\n",
    "    zmin = np.min(cluster[:, 2])\n",
    "    base_points = cluster[cluster[:, 2] <= zmin + 0.2 * np.ptp(cluster[:, 2])]\n",
    "    print(f\"Base points: {len(base_points)}\")\n",
    "    return base_points.mean(axis=0) if len(base_points) > 0 else cluster.mean(axis=0)"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "91fbe8c5",
   "metadata": {},
   "outputs": [],
   "source": [
    "def detect_piles_from_pointcloud(pts):\n",
    "    \"\"\"End-to-end detection pipeline.\"\"\"\n",
    "    print(\"Running geometric detection pipeline...\")\n",
    "\n",
    "    elevated = filter_elevated_points(pts, MIN_HEIGHT)\n",
    "    print(f\"Points above {MIN_HEIGHT}m: {len(elevated):,}\")\n",
    "\n",
    "    if len(elevated) < MIN_POINTS_PER_CLUSTER:\n",
    "        return []\n",
    "\n",
    "    clusters = cluster_vertical_structures(elevated)\n",
    "    print(f\"Found {len(clusters)} structure candidates\")\n",
    "\n",
    "    results = []\n",
    "    for i, cluster in enumerate(clusters):\n",
    "        if len(cluster) > MAX_CLUSTER_SIZE:\n",
    "            continue  # Optional: skip noisy blobs\n",
    "\n",
    "        conf, h, w, d, n, ds = analyze_cluster(cluster)\n",
    "        if conf >= CONFIDENCE_THRESHOLD:\n",
    "            center = compute_center(cluster)\n",
    "            results.append({\n",
    "                \"x\": center[0], \"y\": center[1], \"z\": center[2], \"confidence\": conf,\n",
    "                \"width\": w, \"height\": h, \"depth\": d,\n",
    "                \"point_count\": n, \"distribution_score\": ds,\n",
    "                \"detection_method\": \"geometric\"\n",
    "            })\n",
    "        else:\n",
    "            print(f\"Rejected cluster {i}: conf={conf:.2f}, h={h:.2f}, w={w:.2f}, pts={n}\")\n",
    "    \n",
    "    print(f\"Detected {len(results)} C-section piles\")\n",
    "    return results\n"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "82524c45",
   "metadata": {},
   "outputs": [],
   "source": [
    "from scipy.spatial import cKDTree\n",
    "\n",
    "def filter_detections_against_ifc(detections, ifc_df, radius=1.5):\n",
    "    \"\"\"Retain only detections that are within a given distance to known IFC piles.\"\"\"\n",
    "    ifc_coords = ifc_df[[\"X\", \"Y\"]].values\n",
    "    tree = cKDTree(ifc_coords)\n",
    "\n",
    "    filtered = []\n",
    "    for det in detections:\n",
    "        dist, _ = tree.query([det[\"x\"], det[\"y\"]], distance_upper_bound=radius)\n",
    "        if dist < radius:\n",
    "            filtered.append(det)\n",
    "\n",
    "    print(f\"{len(filtered)} detections matched IFC locations (within {radius}m)\")\n",
    "    return filtered"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "b1c8bf59",
   "metadata": {},
   "outputs": [],
   "source": [
    "# === OPTIONAL: Load Geometric Detection Results ===\n",
    "# Note: Geometric detection is now in rule_based/01_geometric_pile_detection.ipynb\n",
    "\n",
    "load_geometric_results = False  # Set to True to load geometric detection results\n",
    "\n",
    "if load_geometric_results:\n",
    "    print(\"Loading geometric detection results for comparison...\")\n",
    "    geo_detections = load_geometric_detections(config)\n",
    "    \n",
    "    if geo_detections:\n",
    "        print(f\"Loaded geometric detections: {len(geo_detections)}\")\n",
    "        print(f\"IFC ground truth has: {ground_truth['count'] if ground_truth else 'N/A'} piles\")\n",
    "        \n",
    "        if ground_truth:\n",
    "            print(f\"Geometric vs IFC ratio: {len(geo_detections) / ground_truth['count']:.3f}\")\n",
    "    else:\n",
    "        print(\"No geometric detection results found\")\n",
    "        print(\"Run rule_based/01_geometric_pile_detection.ipynb first to generate results\")\n",
    "else:\n",
    "    print(\"Skipping geometric detection - using IFC ground truth only\")\n",
    "    geo_detections = []"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "e29c6bf0",
   "metadata": {},
   "outputs": [],
   "source": [
    "import matplotlib.pyplot as plt\n",
    "\n",
    "plt.figure(figsize=(10, 6))\n",
    "plt.scatter(points[:, 0], points[:, 1], c='gray', s=1, alpha=0.2)\n",
    "for d in geo_detections:\n",
    "    plt.scatter(d['x'], d['y'], c='orange', s=50, edgecolor='black')\n",
    "plt.axis('equal')\n",
    "plt.title(f\"C-section Pile Detections: {len(geo_detections)}\")\n",
    "plt.xlabel(\"X\"), plt.ylabel(\"Y\")\n",
    "plt.grid(True)\n",
    "plt.show()\n"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "41f800f3",
   "metadata": {},
   "outputs": [],
   "source": [
    "from tabulate import tabulate\n",
    "print(\"Geo detections:\", tabulate(geo_detections))\n"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "454df0ad",
   "metadata": {},
   "outputs": [],
   "source": [
    "# SECTION 4: PointNet++-style model for binary classification\n",
    "\n",
    "class PointNetPPClassifier(tf.keras.Model):\n",
    "    def __init__(self, num_classes=2):\n",
    "        super().__init__()\n",
    "        self.net = tf.keras.Sequential([\n",
    "            tf.keras.layers.Conv1D(64, 1, activation='relu'),\n",
    "            tf.keras.layers.BatchNormalization(),\n",
    "            tf.keras.layers.Conv1D(128, 1, activation='relu'),\n",
    "            tf.keras.layers.BatchNormalization(),\n",
    "            tf.keras.layers.Conv1D(256, 1, activation='relu'),\n",
    "            tf.keras.layers.BatchNormalization(),\n",
    "            tf.keras.layers.GlobalMaxPooling1D(),\n",
    "            tf.keras.layers.Dense(256, activation='relu'),\n",
    "            tf.keras.layers.Dropout(0.3),\n",
    "            tf.keras.layers.Dense(128, activation='relu'),\n",
    "            tf.keras.layers.Dropout(0.3),\n",
    "            tf.keras.layers.Dense(num_classes, activation='softmax')\n",
    "        ])\n",
    "\n",
    "    def call(self, x, training=False):\n",
    "        return self.net(x, training=training)\n"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "75c6e209",
   "metadata": {},
   "outputs": [],
   "source": [
    "def extract_patch_spatial(center, points, config):\n",
    "    \"\"\"Extract patch using spatial radius instead of nearest neighbors\"\"\"\n",
    "    try:\n",
    "        # Calculate distances from center\n",
    "        distances = np.linalg.norm(points[:, :3] - center[:3], axis=1)\n",
    "        \n",
    "        # Filter points within radius\n",
    "        within_radius = distances <= config.patch_radius\n",
    "        patch_points = points[within_radius]\n",
    "        \n",
    "        if len(patch_points) < config.min_points_per_patch:\n",
    "            return None\n",
    "        \n",
    "        # Sample or pad to consistent size\n",
    "        if len(patch_points) > config.num_points_per_patch:\n",
    "            # Randomly sample points\n",
    "            indices = np.random.choice(len(patch_points), config.num_points_per_patch, replace=False)\n",
    "            patch_points = patch_points[indices]\n",
    "        elif len(patch_points) < config.num_points_per_patch:\n",
    "            # Pad with repeated points\n",
    "            needed = config.num_points_per_patch - len(patch_points)\n",
    "            indices = np.random.choice(len(patch_points), needed, replace=True)\n",
    "            padding = patch_points[indices]\n",
    "            patch_points = np.vstack([patch_points, padding])\n",
    "        \n",
    "        # Center the patch (preserve scale information)\n",
    "        patch_center = patch_points.mean(axis=0)\n",
    "        patch_points_centered = patch_points - patch_center\n",
    "        \n",
    "        # Optional: normalize by patch radius (not global max)\n",
    "        max_dist = np.max(np.linalg.norm(patch_points_centered[:, :3], axis=1))\n",
    "        if max_dist > 0:\n",
    "            patch_points_centered[:, :3] /= max_dist\n",
    "        \n",
    "        return patch_points_centered\n",
    "        \n",
    "    except Exception as e:\n",
    "        print(f\"[WARN] Failed to extract patch at center {center}: {e}\")\n",
    "        return None\n",
    "\n",
    "# Legacy function for compatibility\n",
    "def extract_patch(center, n_points=128, fallback_n=64):\n",
    "    \"\"\"Legacy patch extraction - use extract_patch_spatial instead\"\"\"\n",
    "    return extract_patch_spatial(center, points, config)"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "b248074d",
   "metadata": {},
   "outputs": [],
   "source": [
    "# === 5. GENERATE TRAINING DATA FROM IFC GROUND TRUTH ===\n",
    "\n",
    "def generate_training_data(points, ground_truth, config):\n",
    "    \"\"\"Generate training patches using IFC ground truth instead of geometric detections\"\"\"\n",
    "    \n",
    "    if ground_truth is None:\n",
    "        print(\"No ground truth data available\")\n",
    "        return None, None\n",
    "    \n",
    "    positive_patches = []\n",
    "    negative_patches = []\n",
    "    failed_positive = 0\n",
    "    failed_negative = 0\n",
    "    \n",
    "    # Extract positive patches from IFC pile locations\n",
    "    pile_coords = ground_truth['coordinates']\n",
    "    num_positives = int(len(pile_coords) * config.positive_sample_ratio)\n",
    "    \n",
    "    print(f\"Extracting positive patches from {num_positives} IFC pile locations...\")\n",
    "    \n",
    "    # Randomly sample pile locations for training\n",
    "    selected_piles = np.random.choice(len(pile_coords), num_positives, replace=False)\n",
    "    \n",
    "    for idx in selected_piles:\n",
    "        pile_center = pile_coords[idx]\n",
    "        patch = extract_patch_spatial(pile_center, points, config)\n",
    "        if patch is not None:\n",
    "            positive_patches.append(patch)\n",
    "        else:\n",
    "            failed_positive += 1\n",
    "    \n",
    "    print(f\"Positive patches extracted: {len(positive_patches)}, Failed: {failed_positive}\")\n",
    "    \n",
    "    # Generate negative patches from areas NOT near piles\n",
    "    num_negatives = int(len(positive_patches) * config.negative_sample_multiplier)\n",
    "    \n",
    "    print(f\"Generating {num_negatives} negative patches...\")\n",
    "    \n",
    "    # Create exclusion zones around known piles\n",
    "    exclusion_radius = config.patch_radius * 2  # Avoid areas too close to piles\n",
    "    \n",
    "    attempts = 0\n",
    "    max_attempts = num_negatives * 10  # Prevent infinite loops\n",
    "    \n",
    "    while len(negative_patches) < num_negatives and attempts < max_attempts:\n",
    "        # Random point from point cloud\n",
    "        random_idx = np.random.randint(0, len(points))\n",
    "        candidate_center = points[random_idx]\n",
    "        \n",
    "        # Check if too close to any pile\n",
    "        distances_to_piles = np.linalg.norm(pile_coords - candidate_center[:3], axis=1)\n",
    "        min_distance = np.min(distances_to_piles)\n",
    "        \n",
    "        if min_distance > exclusion_radius:\n",
    "            patch = extract_patch_spatial(candidate_center, points, config)\n",
    "            if patch is not None:\n",
    "                negative_patches.append(patch)\n",
    "            else:\n",
    "                failed_negative += 1\n",
    "        \n",
    "        attempts += 1\n",
    "    \n",
    "    print(f\"Negative patches generated: {len(negative_patches)}, Failed: {failed_negative}\")\n",
    "    \n",
    "    if len(positive_patches) == 0 or len(negative_patches) == 0:\n",
    "        print(\"Insufficient training data generated\")\n",
    "        return None, None\n",
    "    \n",
    "    # Combine patches and labels\n",
    "    all_patches = positive_patches + negative_patches\n",
    "    all_labels = [1] * len(positive_patches) + [0] * len(negative_patches)\n",
    "    \n",
    "    # Convert to numpy arrays\n",
    "    patches = np.array(all_patches)\n",
    "    labels = np.array(all_labels)\n",
    "    \n",
    "    print(f\"\\nTraining data summary:\")\n",
    "    print(f\"  Total samples: {len(patches)}\")\n",
    "    print(f\"  Patch shape: {patches.shape}\")\n",
    "    print(f\"  Positive samples: {len(positive_patches)}\")\n",
    "    print(f\"  Negative samples: {len(negative_patches)}\")\n",
    "    print(f\"  Class balance: {len(positive_patches)/(len(positive_patches)+len(negative_patches)):.2f}\")\n",
    "    \n",
    "    return patches, labels\n",
    "\n",
    "# Generate training data\n",
    "patches, labels = generate_training_data(points, ground_truth, config)\n",
    "\n",
    "if patches is not None:\n",
    "    print(\"\\nTraining data generated successfully!\")\n",
    "else:\n",
    "    print(\"Failed to generate training data\")\n"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "8c298f6b",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Generate random negative patches\n",
    "negative_patches = []\n",
    "neg_centers = points[np.random.choice(len(points), size=1000, replace=False)]\n",
    "for c in neg_centers:\n",
    "    patch = extract_patch(c)\n",
    "    if patch is not None:\n",
    "        negative_patches.append(patch)\n"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "5c5acd39",
   "metadata": {},
   "outputs": [],
   "source": [
    "import random\n",
    "from sklearn.utils import shuffle\n",
    "\n",
    "# Balance: sample negatives to match number of positives\n",
    "num_pos = len(positive_patches)\n",
    "num_neg = len(negative_patches)\n",
    "\n",
    "if num_pos == 0:\n",
    "    print(\"No positive patches found. Skipping training.\")\n",
    "    patches = np.array([])\n",
    "    labels = np.array([])\n",
    "else:\n",
    "    if num_neg > num_pos:\n",
    "        negative_patches = random.sample(negative_patches, num_pos)\n",
    "        print(f\"Balanced dataset: {num_pos} positives, {num_pos} negatives\")\n",
    "    else:\n",
    "        print(f\"Imbalanced: {num_pos} positives, {num_neg} negatives\")\n",
    "\n",
    "    # Combine and shuffle\n",
    "    patches = np.array(positive_patches + negative_patches)\n",
    "    labels = np.array([1] * len(positive_patches) + [0] * len(negative_patches))\n",
    "    patches, labels = shuffle(patches, labels, random_state=42)\n",
    "\n",
    "print(f\"\\nTraining patches: {patches.shape}\")\n",
    "print(f\"Positive: {len(positive_patches)}, ❌ Negative: {len(negative_patches)}\")\n"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "8b01351a",
   "metadata": {},
   "outputs": [],
   "source": [
    "# SECTION 6: Train PointNet++ model + Save History\n",
    "\n",
    "import matplotlib.pyplot as plt\n",
    "from datetime import datetime\n",
    "\n",
    "# === 6. TRAIN MODEL WITH CORRECTED DATA ===\n",
    "\n",
    "if patches is None:\n",
    "    print(\"No training data available. Skipping training.\")\n",
    "    final_detections = []\n",
    "else:\n",
    "    # Split data for training and validation\n",
    "    from sklearn.model_selection import train_test_split\n",
    "    \n",
    "    X_train, X_val, y_train, y_val = train_test_split(\n",
    "        patches, labels, \n",
    "        test_size=config.validation_split, \n",
    "        stratify=labels, \n",
    "        random_state=42\n",
    "    )\n",
    "    \n",
    "    print(f\"Training set: {len(X_train)} samples\")\n",
    "    print(f\"Validation set: {len(X_val)} samples\")\n",
    "    \n",
    "    # Initialize and compile model\n",
    "    model = PointNetPPClassifier(num_classes=config.num_classes)\n",
    "    model.compile(\n",
    "        optimizer=tf.keras.optimizers.Adam(learning_rate=config.learning_rate),\n",
    "        loss='sparse_categorical_crossentropy', \n",
    "        metrics=['accuracy']\n",
    "    )\n",
    "    \n",
    "    # Train model\n",
    "    print(\"\\nStarting training...\")\n",
    "    history = model.fit(\n",
    "        X_train,\n",
    "        y_train,\n",
    "        epochs=config.epochs,\n",
    "        batch_size=config.batch_size,\n",
    "        validation_data=(X_val, y_val),\n",
    "        verbose=1\n",
    "    )\n",
    "\n",
    "    # Plot training history\n",
    "    plt.figure(figsize=(10, 4))\n",
    "    plt.plot(history.history['accuracy'], label='Train Acc')\n",
    "    plt.plot(history.history['val_accuracy'], label='Val Acc')\n",
    "    plt.plot(history.history['loss'], label='Train Loss')\n",
    "    plt.plot(history.history['val_loss'], label='Val Loss')\n",
    "    plt.title('PointNet++ Training History')\n",
    "    plt.xlabel('Epoch')\n",
    "    plt.ylabel('Accuracy / Loss')\n",
    "    plt.grid(True)\n",
    "    plt.legend()\n",
    "    plt.tight_layout()\n",
    "    plt.savefig(output_dir / f'pointnetpp_training_history_{ground_method}.png', dpi=300)\n",
    "    plt.show()\n",
    "\n",
    "    # Optional: Save model\n",
    "    if config.save_model:\n",
    "        model.save(output_dir / f'pointnetpp_model_{ground_method}.h5')\n",
    "        print(\"Model saved.\")\n",
    "\n",
    "    # === 7. VALIDATE AGAINST IFC GROUND TRUTH ===\n",
    "    \n",
    "    def validate_against_ifc_ground_truth(model, points, ground_truth, config):\n",
    "        \"\"\"Validate model predictions against IFC pile locations\"\"\"\n",
    "        \n",
    "        if ground_truth is None or model is None:\n",
    "            print(\"Cannot validate: missing ground truth or model\")\n",
    "            return None\n",
    "        \n",
    "        pile_coords = ground_truth['coordinates']\n",
    "        pile_ids = ground_truth['ids']\n",
    "        \n",
    "        print(f\"\\nValidating against {len(pile_coords)} IFC pile locations...\")\n",
    "        \n",
    "        # Test model on IFC pile locations\n",
    "        true_positives = 0\n",
    "        false_negatives = 0\n",
    "        validation_results = []\n",
    "        \n",
    "        for i, pile_center in enumerate(pile_coords[:100]):  # Test subset for speed\n",
    "            patch = extract_patch_spatial(pile_center, points, config)\n",
    "            if patch is not None:\n",
    "                # Predict with model\n",
    "                patch_batch = np.expand_dims(patch, axis=0)\n",
    "                prediction = model.predict(patch_batch, verbose=0)\n",
    "                predicted_class = np.argmax(prediction[0])\n",
    "                confidence = np.max(prediction[0])\n",
    "                \n",
    "                validation_results.append({\n",
    "                    'pile_id': pile_ids[i],\n",
    "                    'coordinates': pile_center,\n",
    "                    'predicted_class': predicted_class,\n",
    "                    'confidence': confidence,\n",
    "                    'is_pile_detected': predicted_class == 1\n",
    "                })\n",
    "                \n",
    "                if predicted_class == 1:  # Model detected pile\n",
    "                    true_positives += 1\n",
    "                else:\n",
    "                    false_negatives += 1\n",
    "            else:\n",
    "                false_negatives += 1\n",
    "        \n",
    "        # Calculate metrics\n",
    "        total_tested = true_positives + false_negatives\n",
    "        recall = true_positives / total_tested if total_tested > 0 else 0\n",
    "        \n",
    "        print(f\"\\nValidation Results:\")\n",
    "        print(f\"  Tested pile locations: {total_tested}\")\n",
    "        print(f\"  True positives (correctly detected): {true_positives}\")\n",
    "        print(f\"  False negatives (missed piles): {false_negatives}\")\n",
    "        print(f\"  Recall (detection rate): {recall:.3f}\")\n",
    "        \n",
    "        # Show confidence distribution\n",
    "        if validation_results:\n",
    "            confidences = [r['confidence'] for r in validation_results if r['is_pile_detected']]\n",
    "            if confidences:\n",
    "                print(f\"  Average confidence for detected piles: {np.mean(confidences):.3f}\")\n",
    "                print(f\"  Confidence range: {np.min(confidences):.3f} - {np.max(confidences):.3f}\")\n",
    "        \n",
    "        return {\n",
    "            'true_positives': true_positives,\n",
    "            'false_negatives': false_negatives,\n",
    "            'recall': recall,\n",
    "            'total_tested': total_tested,\n",
    "            'results': validation_results\n",
    "        }\n",
    "    \n",
    "    # Validate model\n",
    "    validation_metrics = validate_against_ifc_ground_truth(model, points, ground_truth, config)\n",
    "    print(\"\\nValidation against IFC ground truth completed!\")\n",
    "\n",
    "\n",
    "    # === 8. INFERENCE AND FINAL DETECTIONS ===\n",
    "    \n",
    "    # Test model on training patches for analysis\n",
    "    if patches is not None and len(patches) > 0:\n",
    "        probs = model.predict(patches)\n",
    "        confidences = probs[:, 1]  # class 1 = pile\n",
    "        preds = confidences >= confidence_threshold\n",
    "        \n",
    "        print(f\"\\nTraining set inference:\")\n",
    "        print(f\"  Total patches: {len(patches)}\")\n",
    "        print(f\"  Predicted as piles: {np.sum(preds)}\")\n",
    "        print(f\"  Average confidence: {np.mean(confidences):.3f}\")\n",
    "    \n",
    "    # Generate final detections based on approach used\n",
    "    final_detections = []\n",
    "    \n",
    "    if load_geometric_results and len(geo_detections) > 0:\n",
    "        # Traditional approach: filter geometric detections with model\n",
    "        print(f\"\\nFiltering {len(geo_detections)} geometric detections with trained model...\")\n",
    "        \n",
    "        for i, det in enumerate(geo_detections):\n",
    "            patch = extract_patch_spatial([det['x'], det['y'], det['z']], points, config)\n",
    "            if patch is not None:\n",
    "                patch_batch = np.expand_dims(patch, axis=0)\n",
    "                prob = model.predict(patch_batch, verbose=0)\n",
    "                confidence = prob[0][1]  # pile class probability\n",
    "                \n",
    "                if confidence >= confidence_threshold:\n",
    "                    det_copy = det.copy()\n",
    "                    det_copy['model_confidence'] = float(confidence)\n",
    "                    det_copy['source'] = 'geometric_filtered'\n",
    "                    final_detections.append(det_copy)\n",
    "        \n",
    "        print(f\"Final detections after model filtering: {len(final_detections)} / {len(geo_detections)}\")\n",
    "    \n",
    "    else:\n",
    "        # New approach: use model predictions on IFC validation results\n",
    "        print(f\"\\nUsing model validation results as final detections...\")\n",
    "        \n",
    "        if 'validation_metrics' in locals() and validation_metrics:\n",
    "            for result in validation_metrics['results']:\n",
    "                if result['is_pile_detected'] and result['confidence'] >= confidence_threshold:\n",
    "                    coords = result['coordinates']\n",
    "                    final_detections.append({\n",
    "                        'x': coords[0],\n",
    "                        'y': coords[1], \n",
    "                        'z': coords[2],\n",
    "                        'confidence': result['confidence'],\n",
    "                        'pile_id': result['pile_id'],\n",
    "                        'source': 'ifc_validated',\n",
    "                        'model_confidence': result['confidence']\n",
    "                    })\n",
    "            \n",
    "            print(f\"Final detections from IFC validation: {len(final_detections)}\")\n",
    "        else:\n",
    "            print(\"No validation results available for final detections\")\n"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "93666ae0",
   "metadata": {},
   "outputs": [],
   "source": [
    "import matplotlib.pyplot as plt\n",
    "\n",
    "plt.figure(figsize=(10, 6))\n",
    "\n",
    "# Background point cloud\n",
    "plt.scatter(points[:, 0], points[:, 1], c='gray', s=1, alpha=0.2, label='All Points')\n",
    "\n",
    "# Final detections after PointNet++\n",
    "for d in final_detections:\n",
    "    plt.scatter(d['x'], d['y'], c='limegreen', s=50, edgecolor='black', label='Final Detection')\n",
    "\n",
    "plt.axis('equal')\n",
    "plt.title(f\"Final C-section Detections After PointNet++: {len(final_detections)}\")\n",
    "plt.xlabel(\"X\")\n",
    "plt.ylabel(\"Y\")\n",
    "plt.grid(True)\n",
    "\n",
    "# Prevent duplicate legend entries\n",
    "handles, labels = plt.gca().get_legend_handles_labels()\n",
    "by_label = dict(zip(labels, handles))\n",
    "plt.legend(by_label.values(), by_label.keys(), loc='upper right')\n",
    "\n",
    "plt.show()\n",
    "\n",
    "\n",
    "probs = model.predict(patches, batch_size=64)\n",
    "print(\"Min:\", np.min(probs[:, 1]), \"Max:\", np.max(probs[:, 1]), \"Mean:\", np.mean(probs[:, 1]))\n"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "037884a5",
   "metadata": {},
   "outputs": [],
   "source": [
    "import seaborn as sns\n",
    "plt.figure(figsize=(8, 4))\n",
    "sns.histplot(confidences, bins=50, kde=True)\n",
    "plt.axvline(confidence_threshold, color='red', linestyle='--', label=f\"Threshold = {confidence_threshold}\")\n",
    "plt.title(\"Model Predicted Confidence Scores (Class = 1)\")\n",
    "plt.xlabel(\"Confidence\")\n",
    "plt.ylabel(\"Count\")\n",
    "plt.legend()\n",
    "plt.grid(True)\n",
    "plt.show()"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "5312bc89",
   "metadata": {},
   "outputs": [],
   "source": [
    "# SECTION 8: Save final detections\n",
    "df = pd.DataFrame(final_detections)\n",
    "df.to_csv(output_dir / f\"c_section_detections_final.csv\", index=False)\n",
    "\n",
    "# === ENHANCED METRICS WITH IFC VALIDATION ===\n",
    "metrics = {\n",
    "    'timestamp': datetime.now().isoformat(),\n",
    "    'site_name': site_name,\n",
    "    'ground_method': ground_method,\n",
    "    'confidence_threshold': confidence_threshold,\n",
    "    'input_points': len(points),\n",
    "    'geometric_detections': len(geo_detections) if 'geo_detections' in locals() else 0,\n",
    "    'final_detections': len(final_detections) if 'final_detections' in locals() else 0,\n",
    "    'ifc_ground_truth_piles': ground_truth['count'] if ground_truth else 0,\n",
    "    'training_samples': len(patches) if patches is not None else 0,\n",
    "    'model_epochs': config.epochs if 'config' in locals() else 0,\n",
    "    'patch_radius': config.patch_radius if 'config' in locals() else 0,\n",
    "    'approach': 'ifc_ground_truth_enhanced'\n",
    "}\n",
    "\n",
    "# Add validation metrics if available\n",
    "if 'validation_metrics' in locals() and validation_metrics:\n",
    "    metrics.update({\n",
    "        'validation_recall': validation_metrics['recall'],\n",
    "        'validation_true_positives': validation_metrics['true_positives'],\n",
    "        'validation_false_negatives': validation_metrics['false_negatives'],\n",
    "        'validation_tested': validation_metrics['total_tested']\n",
    "    })\n",
    "\n",
    "with open(output_dir / f\"metrics.json\", 'w') as f:\n",
    "    json.dump(metrics, f, indent=2)\n",
    "\n",
    "print(\"Results saved.\")\n",
    "\n",
    "\n",
    "# SECTION 9: Visualization (Top view)\n",
    "\n",
    "plt.figure(figsize=(10, 6))\n",
    "plt.scatter(points[:, 0], points[:, 1], c='gray', s=1, alpha=0.3)\n",
    "for d in final_detections:\n",
    "    plt.scatter(d['x'], d['y'], c='orange', s=100, edgecolor='black')\n",
    "plt.title(f\"C-section Pile Detections: {len(final_detections)}\")\n",
    "plt.xlabel(\"X\")\n",
    "plt.ylabel(\"Y\")\n",
    "plt.axis(\"equal\")\n",
    "plt.grid(True)\n",
    "plt.show()\n"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "120e7ac8",
   "metadata": {},
   "outputs": [],
   "source": []
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "mimetype": "text/x-python",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.11.11"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 5
}
