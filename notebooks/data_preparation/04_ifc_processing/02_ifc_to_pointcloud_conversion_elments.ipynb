# Parameters
ifc_file_path = "../../../data/raw/trino_enel/ifc/GRE.EEC.S.00.IT.P.14353.00.265.ifc"
output_dir = "../../../data/processed/trino_enel/ifc_pointclouds"
site_name = "trino_enel"

POINT_DENSITY = 0.05  # 5cm - Aligned with 1cm GSD from drone SOP
GSD_DRONE = 0.01      # 1cm GSD from Datasee.AI SOP
DENSITY_RATIO = POINT_DENSITY / GSD_DRONE  # 5:1 ratio for compatibility

# Point cloud generation parameters
# include_element_types = ["IfcPile", "IfcBeam", "IfcColumn", "IfcSlab"]

# Pile-focused element types (based on SOP requirements)
PILE_ELEMENT_TYPES = [
    "IfcPile",           # Primary: Actual pile elements
    "IfcColumn",         # Secondary: Vertical structural elements
    #"IfcFoundation",     # Context: Foundation elements (Not in IFC4)
    "IfcFooting",        # Context: Pile caps and footings
    "IfcBeam"            # Context: Grade beams connecting piles
]

# Analysis parameters aligned with construction tolerances
MIN_PILE_SIZE = 0.15        # 15cm minimum (smaller piles included)
MAX_POINTS_PER_PILE = 500000  # High detail for critical elements
TOLERANCE_THRESHOLD = 0.02   # 2cm - typical pile positioning tolerance


#min_element_size = 0.5  # Minimum element size to include (meters)
#max_points_per_element = 10000  # Limit points per element


import ifcopenshell
import ifcopenshell.geom
import numpy as np
import open3d as o3d
from pathlib import Path
import json
import logging
from typing import List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')
from pyproj import Transformer, CRS

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Coordinate system (update based on your site)
SITE_CRS_EPSG = 32633  # Update to match your drone survey CRS
TARGET_CRS = CRS.from_epsg(SITE_CRS_EPSG)

# Setup output directory
output_path = Path(output_dir)
output_path.mkdir(parents=True, exist_ok=True)

print("IFC TO POINT CLOUD CONVERSION")
print(f"IFC file: {Path(ifc_file_path).name}")
print(f"Output directory: {output_path}")

print("=== DATASEE.AI SOP-ALIGNED PILE ANALYSIS ===")

print(f"Minimum pile size: {MIN_PILE_SIZE}m")
print(f"Point density: {POINT_DENSITY}m (5cm)")
print(f"Drone GSD: {GSD_DRONE}m (1cm)")
print(f"Resolution ratio: {DENSITY_RATIO:.1f}:1")
print(f"Maximum points per pile: {MAX_POINTS_PER_PILE}")
print(f"Element types: {PILE_ELEMENT_TYPES}")
print(f"Site CRS: {SITE_CRS_EPSG}")
print(f"Target CRS: {TARGET_CRS}")
print(f"Tolerance threshold: {TOLERANCE_THRESHOLD}m")

# =============================================================================
# SOP COMPLIANCE TRACKING
# =============================================================================

sop_compliance = {
    "flight_geometry": "Crosshatch + Nadir (from SOP)",
    "gsd_drone": f"{GSD_DRONE}m",
    "point_density_ifc": f"{POINT_DENSITY}m", 
    "resolution_ratio": f"{DENSITY_RATIO:.1f}:1",
    "pile_analysis_focus": True,
    "geometric_accuracy": "Sub-centimeter (SOP compliant)",
    "data_structure": "Version controlled output",
    "research_framework": "Datasee.AI SOP aligned"
}

from pyproj import Transformer, CRS

def dms_to_decimal(dms):
    if not dms or len(dms) < 3:
        return None
    
    # Convert to list and ensure we have at least 4 values
    dms_values = list(dms)
    while len(dms_values) < 4:
        dms_values.append(0)
    
    deg, minute, sec, micro = dms_values[:4]
    sign = -1 if deg < 0 else 1
    return sign * (abs(deg) + minute / 60 + sec / 3600 + micro / 3600 / 1e6)

def get_utm_crs(lat, lon):
    """Get EPSG code for UTM zone based on latitude and longitude."""
    zone_number = int((lon + 180) / 6) + 1
    if lat >= 0:
        return CRS.from_epsg(32600 + zone_number)  # Northern hemisphere
    else:
        return CRS.from_epsg(32700 + zone_number)  # Southern hemisphere


from tqdm import tqdm

# Load IFC file
print("Loading IFC file...")
ifc_file = ifcopenshell.open(ifc_file_path)

# Setup geometry settings for mesh extraction
settings = ifcopenshell.geom.settings()
settings.set(settings.USE_WORLD_COORDS, True)
#settings.set(settings.USE_WORLD_COORDS, False)

settings.set(settings.WELD_VERTICES, True)
#settings.set(settings.SEW_SHELLS, True)

print(f"IFC Schema: {ifc_file.schema}")

# Count elements to process
total_elements = 0
for element_type in PILE_ELEMENT_TYPES:
    elements = ifc_file.by_type(element_type)
    total_elements += len(elements)
    print(f"  {element_type}: {len(elements)} elements")

print(f"\nTotal elements to convert: {total_elements}")

lat_dms = ifc_file.by_type("IfcSite")[0].RefLatitude
lon_dms = ifc_file.by_type("IfcSite")[0].RefLongitude
elev = ifc_file.by_type("IfcSite")[0].RefElevation

# --- Site Metadata Check ---
site = ifc_file.by_type("IfcSite")
if site:
    site = site[0]
    lat = dms_to_decimal(lat_dms)
    lon = dms_to_decimal(lon_dms)

    print("\n=== IFC SITE GEOLOCATION ===")
    print(f"  Raw RefLatitude:  {lat_dms}")
    print(f"  Raw RefLongitude: {lon_dms}")
    print(f"  Elevation:        {elev} m")

    if lat is not None and lon is not None:
        print(f"  Decimal Latitude: {lat:.8f}")
        print(f"  Decimal Longitude:{lon:.8f}")

        # Optional override if out of expected range for Italy
        if not (7.0 <= lon <= 19.0 and 36.0 <= lat <= 47.5):
            print("  Detected coordinates outside Italy — overriding to Milan.")
            lat, lon = 45.4642, 9.1900

        # Compute UTM transformation
        #source_crs = CRS.from_epsg(4326)
        #target_crs = get_utm_crs(lat, lon)
        
        target_crs = CRS.from_epsg(32633)
        source_crs = CRS.from_epsg(4326)

        transformer = Transformer.from_crs(source_crs, target_crs, always_xy=True)
        utm_x, utm_y = transformer.transform(lon, lat)

        print(f"\n=== UTM Zone EPSG:   {target_crs.to_epsg()}")
        print(f"  UTM CRS:         {target_crs.to_string()}")
        print(f"  Transformed Site Origin (IFC local → UTM):")
        print(f"  X = {utm_x:.2f}, Y = {utm_y:.2f}")
        print("GCP benchmark alignment ready")

    else:
        print("Incomplete geolocation metadata. Cannot compute UTM.")



# from pyproj import Transformer, CRS


# # Choose target CRS: same as drone LAS (EPSG:32632) or appropriate UTM from lat/lon
# target_crs = CRS.from_epsg(32632)  # Your drone's CRS
# source_crs = CRS.from_epsg(4326)   # WGS84 from lat/lon

# transformer = Transformer.from_crs(source_crs, target_crs, always_xy=True)

# # Transform the site reference point to get the shift
# utm_x, utm_y = transformer.transform(lon, lat)
# print(f"Transformed site origin (IFC local → UTM): X={utm_x:.2f}, Y={utm_y:.2f}")

# CORRECTED FUNCTIONS - BUG FIXES

def extract_mesh_from_element(element, settings):
    """Extract mesh geometry from IFC element."""
    try:
        if not element.Representation:
            return None, None, None

        shape = ifcopenshell.geom.create_shape(settings, element)
        if shape and shape.geometry:
            # Get vertices and faces
            vertices = np.array(shape.geometry.verts).reshape(-1, 3)
            faces = np.array(shape.geometry.faces).reshape(-1, 3)
            
            # Extract normals for surface analysis (twist detection)
            normals = None
            try:
                if hasattr(shape.geometry, 'normals'):
                    normals = np.array(shape.geometry.normals).reshape(-1, 3)
            except:
                pass # silent fail

            if len(vertices) > 0 and len(faces) > 0:
                return vertices, faces, normals  # FIXED: Return normals too
    except Exception as e:
        logger.warning(f"Could not extract mesh for {element.GlobalId}: {str(e)[:50]}...")
    
    return None, None, None  # FIXED: Return 3 values consistently

def mesh_to_pointcloud(vertices, faces, point_density=0.05, max_points=500000):  # FIXED: Updated defaults
    """Convert mesh to point cloud using surface sampling."""
    try:
        # Create Open3D mesh
        mesh = o3d.geometry.TriangleMesh()
        mesh.vertices = o3d.utility.Vector3dVector(vertices)
        mesh.triangles = o3d.utility.Vector3iVector(faces)
        
        # Clean mesh for accurate analysis
        mesh.remove_degenerate_triangles()
        mesh.remove_duplicated_triangles()
        mesh.remove_duplicated_vertices()
        mesh.compute_triangle_normals()
        mesh.compute_vertex_normals()

        # Calculate surface area with fallback
        try:
            surface_area = mesh.get_surface_area()
        except:
            # Fallback calculation if get_surface_area fails
            bbox = mesh.get_axis_aligned_bounding_box()
            extent = bbox.get_extent()
            surface_area = 2 * (extent[0] * extent[1] + extent[1] * extent[2] + extent[0] * extent[2])

        num_points = min(int(surface_area / (point_density ** 2)), max_points)
        num_points = max(num_points, 100)  # Minimum detail for analysis

        # Sample points from mesh surface
        point_cloud = mesh.sample_points_uniformly(number_of_points=num_points)
        points = np.asarray(point_cloud.points)
        
        # FIXED: Check if normals exist before accessing
        normals = None
        if point_cloud.has_normals():
            normals = np.asarray(point_cloud.normals)

        return points, normals
        
    except Exception as e:
        logger.warning(f"Mesh to point cloud conversion failed: {e}")
        return None, None

def filter_element_by_size(vertices, min_size=0.15):  # FIXED: Smaller default for piles
    """Filter elements by minimum size and analyze pile geometry."""
    if vertices is None or len(vertices) == 0:
        return None
    
    # Calculate bounding box dimensions
    bbox_min = np.min(vertices, axis=0)
    bbox_max = np.max(vertices, axis=0)
    dimensions = bbox_max - bbox_min
    center = (bbox_min + bbox_max) / 2
 
    # Pile-specific analysis
    height = dimensions[2]
    diameter = max(dimensions[0], dimensions[1])
    volume = np.prod(dimensions)

    # FIXED: Actually use the size check result
    meets_size_criteria = np.any(dimensions > min_size)

    # Define constants if not already defined
    #MIN_PILE_SIZE = 0.15  # 15cm minimum
    #TOLERANCE_THRESHOLD = 0.02  # 2cm tolerance

    # Determine if element meets pile criteria
    is_pile_like = (
        height > diameter * 1.5 and  # Height > 1.5x diameter (vertical element)
        diameter >= MIN_PILE_SIZE and  # Minimum pile size
        volume > 0.001 and  # Minimum volume
        meets_size_criteria  # FIXED: Include size check
    )

    return {
        'is_pile_like': is_pile_like,
        'height': height,
        'diameter': diameter,
        'volume': volume,
        'center': center.tolist(),
        'dimensions': dimensions.tolist(),
        'meets_tolerance': diameter >= TOLERANCE_THRESHOLD * 10,  # 10x tolerance minimum
        'meets_size_criteria': meets_size_criteria  # FIXED: Include this check
    }

def filter_element_by_size_permissive(vertices, min_size=0.15):
    """More permissive version for testing."""
    if vertices is None or len(vertices) == 0:
        return None
    
    bbox_min = np.min(vertices, axis=0)
    bbox_max = np.max(vertices, axis=0)
    dimensions = bbox_max - bbox_min
    center = (bbox_min + bbox_max) / 2
 
    height = dimensions[2]
    diameter = max(dimensions[0], dimensions[1])
    volume = np.prod(dimensions)

    meets_size_criteria = np.any(dimensions > min_size)

    # Very permissive for testing
    MIN_PILE_SIZE = 0.01  # 1cm minimum
    
    is_pile_like = (
        height > diameter * 0.5 and  # Much more permissive height ratio
        diameter >= MIN_PILE_SIZE and
        volume > 0.0001  # Smaller minimum volume
    )

    # VERY permissive tolerance - almost everything passes
    meets_tolerance = diameter >= 0.01  # Just 1cm minimum

    return {
        'is_pile_like': is_pile_like,
        'height': height,
        'diameter': diameter,
        'volume': volume,
        'center': center.tolist(),
        'dimensions': dimensions.tolist(),
        'meets_tolerance': meets_tolerance,
        'meets_size_criteria': meets_size_criteria
    }


 
print("Point cloud conversion functions ready")


print(f"\nAnalyzing pile elements...")

# 1. Count elements by type — this gives visibility
element_counts = {}
total_elements = 0
for element_type in PILE_ELEMENT_TYPES:
    elements = ifc_file.by_type(element_type)
    count = len(elements)
    element_counts[element_type] = count
    total_elements += count
    print(f"  {element_type}: {count} elements")

print(f"Total pile-related elements: {total_elements}")



# 2. Begin processing
print(f"\nGenerating SOP-aligned point cloud...")

all_points = []
all_normals = []
pile_analysis = []
processing_stats = {
    'total_processed': 0,
    'successful_conversions': 0,
    'failed_conversions': 0,
    'pile_like_elements': 0,
    'points_generated': 0
}

for element_type in PILE_ELEMENT_TYPES:
    elements = ifc_file.by_type(element_type)
    if not elements:
        continue
    
    print(f"\nProcessing {len(elements)} {element_type} elements...")
    
    for i, element in enumerate(elements):
        processing_stats['total_processed'] += 1
        
        try:
            # Extract geometry
            vertices, faces, normals = extract_mesh_from_element(element, settings)
            
            if vertices is not None and faces is not None:
                geometry_analysis = filter_element_by_size_permissive(vertices, min_size=0.15)
                
                if geometry_analysis and geometry_analysis['meets_tolerance'] and geometry_analysis['meets_size_criteria']:
                    points, pc_normals = mesh_to_pointcloud(vertices, faces, point_density=POINT_DENSITY, max_points=MAX_POINTS_PER_PILE)
                    
                    if points is not None and len(points) > 0:
                        all_points.append(points)
                        if pc_normals is not None:
                            all_normals.append(pc_normals)
                        
                        pile_analysis.append({
                            'element_id': element.GlobalId,
                            'name': getattr(element, 'Name', None),
                            'type': element.is_a(),
                            'point_count': len(points),
                            'geometry_analysis': geometry_analysis,
                            'sop_compliant': True,
                            'point_density_actual': POINT_DENSITY,
                            'analysis_ready': True
                        })
                        
                        processing_stats['successful_conversions'] += 1
                        processing_stats['points_generated'] += len(points)
                        
                        if geometry_analysis['is_pile_like']:
                            processing_stats['pile_like_elements'] += 1
                    else:
                        processing_stats['failed_conversions'] += 1
                else:
                    processing_stats['failed_conversions'] += 1
                    if i < 5 and geometry_analysis:
                        print(f"    Element {element.GlobalId}: tolerance={geometry_analysis['meets_tolerance']}, size={geometry_analysis.get('meets_size_criteria', False)}")
            else:
                processing_stats['failed_conversions'] += 1
        
        except Exception as e:
            processing_stats['failed_conversions'] += 1
            if i < 5:
                print(f"    Exception for {element.GlobalId}: {e}")
        
        if (i + 1) % 50 == 0:
            print(f"  Progress: {i+1}/{len(elements)}, Success: {processing_stats['successful_conversions']}")


# Check if we have results
print(all_points)
if all_points:
    # Combine all point clouds
    combined_points = np.vstack(all_points)
    combined_normals = np.vstack(all_normals) if all_normals else None
    
    # Apply coordinate transformation if available (add your transform code here if needed)
    # combined_points[:, 0] += origin_x  # Uncomment if you have origin coordinates
    # combined_points[:, 1] += origin_y
    # combined_points[:, 2] += origin_z
    
    # Print results summary
    print(f"\nSOP-ALIGNED POINT CLOUD GENERATED")
    print(f"Total points: {len(combined_points):,}")
    print(f"Point density: {POINT_DENSITY}m (SOP compliant)")
    print(f"Pile-like elements: {processing_stats['pile_like_elements']}")
    print(f"Success rate: {processing_stats['successful_conversions']}/{processing_stats['total_processed']} ({100*processing_stats['successful_conversions']/processing_stats['total_processed']:.1f}%)")


import pandas as pd
import numpy as np
import json
from pathlib import Path
from pyproj import CRS, Transformer
import open3d as o3d

if combined_points is not None and len(combined_points) > 0:
    print(f"\nPoint Cloud: {len(combined_points):,} points")
    
    bounds_min = combined_points.min(axis=0)
    bounds_max = combined_points.max(axis=0)
    center = combined_points.mean(axis=0)
    extent = bounds_max - bounds_min

    print(f"  Bounds X: {bounds_min[0]:.2f} – {bounds_max[0]:.2f}")
    print(f"  Bounds Y: {bounds_min[1]:.2f} – {bounds_max[1]:.2f}")
    print(f"  Bounds Z: {bounds_min[2]:.2f} – {bounds_max[2]:.2f}")
    print(f"  Center: {np.round(center, 2).tolist()}")
    print(f"  Extent: {np.round(extent, 2).tolist()}")

    # Create Open3D point cloud object
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(combined_points)

    # File naming
    ifc_name = Path(ifc_file_path).stem
    paths = {
        "ply": output_path / f"{ifc_name}_pointcloud.ply",
        "data_driven": output_path / f"{ifc_name}_data_driven.ply",
        "crs": output_path / f"{ifc_name}_crs.json",
        "element_info": output_path / f"{ifc_name}_element_info.json",
        "summary": output_path / f"{ifc_name}_conversion_summary.json"
    }

    try:
        o3d.io.write_point_cloud(str(paths["ply"]), pcd)
        o3d.io.write_point_cloud(str(paths["data_driven"]), pcd)
        print(f"Saved PLY: {paths['ply'].name}, {paths['data_driven'].name}")
    except Exception as e:
        print(f"[ERROR] Failed to save PLY: {e}")

    # Save CRS metadata
    try:
        #target_crs = get_utm_crs(lat, lon)
        target_crs = CRS.from_epsg(32633)
        print(f"Target CRS: {target_crs}")
        transformer = Transformer.from_crs(CRS.from_epsg(4326), target_crs, always_xy=True)
        utm_x, utm_y = transformer.transform(lon, lat)

        # Convert string to Path object
        ifc_path = Path(ifc_file_path)
        crs_path = ifc_path.with_name(ifc_path.stem + "_crs.json")

        crs_info = {
            'epsg': target_crs.to_epsg(),
            'utm_zone': target_crs.to_string(),
            'origin_utm_x': utm_x,
            'origin_utm_y': utm_y
        }            

        with open(crs_path, "w") as f:
            json.dump(crs_info, f, indent=2)

        print(f"Saved CRS metadata: {paths['crs'].name}")
    except Exception as e:
        print(f"[WARNING] Failed to write CRS metadata: {e}")

    # Save element info
    try:
        with open(paths["element_info"], 'w') as f:
            json.dump(element_info, f, indent=2)
        print(f"Saved element info: {paths['element_info'].name}")
    except Exception as e:
        print(f"[WARNING] Failed to save element info: {e}")

    # Save summary
    try:
        summary = {
            'ifc_file': ifc_name,
            'conversion_date': pd.Timestamp.now().isoformat(),
            'parameters': {
                'point_density': point_density,
                'min_element_size': min_element_size,
                'max_points_per_element': max_points_per_element,
                'element_types': include_element_types
            },
            'statistics': conversion_stats,
            'point_cloud_properties': {
                'total_points': len(combined_points),
                'bounds': dict(zip(['x_min', 'y_min', 'z_min'], bounds_min)),
                'bounds_max': dict(zip(['x_max', 'y_max', 'z_max'], bounds_max)),
                'center': center.tolist(),
                'extent': extent.tolist()
            },
            'files_generated': {k: str(v) for k, v in paths.items()}
        }

        with open(paths["summary"], 'w') as f:
            json.dump(summary, f, indent=2)

        print(f"Saved summary: {paths['summary'].name}")
    except Exception as e:
        print(f"[WARNING] Failed to write summary: {e}")

    print(f"\nIFC TO POINT CLOUD CONVERSION COMPLETE — Output in: {output_path}")

else:
    print("\nNo point cloud generated — check IFC input or filters.")


import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import open3d as o3d

# Skip if no point cloud
if combined_points is None or len(combined_points) == 0:
    print("No point cloud data available for visualization.")
else:
    print("=== POINT CLOUD ANALYSIS ===")

    # ── 1. Basic Stats ─────────────────────────────────────────────
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle(f'IFC Point Cloud Analysis - {len(combined_points):,} Points', fontsize=16, fontweight='bold')

    for i, axis in enumerate(['X', 'Y', 'Z']):
        axes[0, i].hist(combined_points[:, i], bins=50, alpha=0.7, color=['red', 'green', 'blue'][i])
        axes[0, i].set(title=f'{axis} Distribution', xlabel=f'{axis} (m)', ylabel='Frequency')
        axes[0, i].grid(True, alpha=0.3)

    sample_size = min(50000, len(combined_points))
    idx = np.random.choice(len(combined_points), sample_size, replace=False)
    sample = combined_points[idx]

    axes[1, 0].scatter(sample[:, 0], sample[:, 1], c=sample[:, 2], cmap='viridis', s=0.1, alpha=0.6)
    axes[1, 0].set(title='Top View (X-Y)', xlabel='X (m)', ylabel='Y (m)')
    axes[1, 0].set_aspect('equal')

    axes[1, 1].scatter(sample[:, 0], sample[:, 2], c=sample[:, 1], cmap='plasma', s=0.1, alpha=0.6)
    axes[1, 1].set(title='Side View (X-Z)', xlabel='X (m)', ylabel='Z (m)')

    axes[1, 2].scatter(sample[:, 1], sample[:, 2], c=sample[:, 0], cmap='coolwarm', s=0.1, alpha=0.6)
    axes[1, 2].set(title='Front View (Y-Z)', xlabel='Y (m)', ylabel='Z (m)')

    plt.tight_layout()
    plt.show()


    # ── 2. 3D Scatter Plot ─────────────────────────────────────────
    print(f"\n3D Visualization (Sample Size: {sample_size:,})")
    fig = plt.figure(figsize=(12, 8))
    ax = fig.add_subplot(111, projection='3d')
    sc = ax.scatter(sample[:, 0], sample[:, 1], sample[:, 2],
                    c=sample[:, 2], cmap='viridis', s=0.5, alpha=0.6)
    ax.set(xlabel='X (m)', ylabel='Y (m)', zlabel='Z (m)', title='3D Point Cloud View')
    plt.colorbar(sc, ax=ax, shrink=0.5, label='Height (Z)')
    plt.show()


    # ── 3. Element Geometry Analysis ───────────────────────────────
    if 'pile_analysis' in locals() and pile_analysis:
        print("\n=== ELEMENT GEOMETRY STATISTICS ===")
        get_vals = lambda key: [e['geometry_analysis'][key] for e in pile_analysis if 'geometry_analysis' in e]
        heights, diameters, volumes = map(get_vals, ['height', 'diameter', 'volume'])
        point_counts = [e['point_count'] for e in pile_analysis]

        df = pd.DataFrame({
            'Metric': ['Height (m)', 'Diameter (m)', 'Volume (m³)', 'Points per Element'],
            'Mean': map(np.mean, [heights, diameters, volumes, point_counts]),
            'Median': map(np.median, [heights, diameters, volumes, point_counts]),
            'Min': map(np.min, [heights, diameters, volumes, point_counts]),
            'Max': map(np.max, [heights, diameters, volumes, point_counts]),
            'Std Dev': map(np.std, [heights, diameters, volumes, point_counts])
        })
        print(df.round(4))

        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        axes[0, 0].hist(heights, bins=30, color='skyblue')
        axes[0, 0].set(title='Height Distribution', xlabel='Height (m)', ylabel='Frequency')
        axes[0, 1].hist(diameters, bins=30, color='lightcoral')
        axes[0, 1].set(title='Diameter Distribution', xlabel='Diameter (m)', ylabel='Frequency')
        axes[1, 0].hist(point_counts, bins=30, color='lightgreen')
        axes[1, 0].set(title='Points per Element', xlabel='Count', ylabel='Frequency')
        axes[1, 1].scatter(diameters, heights, s=10, alpha=0.6)
        axes[1, 1].set(title='Height vs Diameter', xlabel='Diameter (m)', ylabel='Height (m)')

        for ax in axes.flat: ax.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.show()


    # ── 4. SOP Compliance ──────────────────────────────────────────
    print("\n=== SOP COMPLIANCE CHECK ===")
    print(f"Point Density Target: {POINT_DENSITY:.2f} m")
    print(f"Total Points: {len(combined_points):,}")
    print(f"Spatial Extent: {extent[0]:.1f} × {extent[1]:.1f} × {extent[2]:.1f} m")
    print(f"Achieved Density: {extent[0]*extent[1]/len(combined_points):.4f} m²/pt")
    print(f"Tolerance Capability: ±{POINT_DENSITY/2:.2f} m")


    # ── 5. Open3D Viewer ───────────────────────────────────────────
    print("\nLaunching Open3D Viewer...")
    try:
        pcd = o3d.geometry.PointCloud()
        pcd.points = o3d.utility.Vector3dVector(sample)
        norm_z = (sample[:, 2] - sample[:, 2].min()) / (sample[:, 2].ptp())
        pcd.colors = o3d.utility.Vector3dVector(plt.cm.viridis(norm_z)[:, :3])
        o3d.visualization.draw_geometries([pcd], window_name=f"Point Cloud - {len(combined_points):,} pts", width=1200, height=800)
    except Exception as e:
        print(f"Open3D Viewer not available: {e}")
