{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Enhanced IFC Metadata Extraction with Geolocation (Fixed)\n", "\n", "## Overview\n", "\n", "This notebook implements comprehensive IFC metadata extraction with robust error handling:\n", "\n", "- **Site geolocation extraction** (RefLatitude, RefLongitude, RefElevation)\n", "- **Geometric centroid calculation** using ifcopenshell.geom\n", "- **Coordinate transformation** to decimal degrees using pyproj\n", "- **Enhanced element metadata** with robust error handling\n", "- **Safe column access** to prevent KeyError issues\n", "\n", "**Key Fix**: <PERSON><PERSON> missing coordinate columns gracefully\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025"]}, {"cell_type": "code", "execution_count": 25, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Parameters\n", "ifc_file_path = \"/Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/raw/trino_enel/ifc/GRE.EEC.S.00.IT.P.14353.00.265.ifc\"\n", "output_dir = \"../../data/processed/trino_enel/ifc_metadata\"\n", "site_name = \"trino_enel\"\n", "\n", "# Processing parameters\n", "target_crs = \"EPSG:32632\"  # UTM Zone 32N for Italy\n", "priority_elements = [\"IfcPile\", \"IfcBeam\", \"IfcColumn\", \"IfcSlab\", \"IfcWall\"]\n", "pile_name_keywords = [\"Pile\", \"pile\", \"PILE\", \"Foundation\", \"FOUNDATION\"]  # Keywords for pile identification\n", "extract_properties = True\n", "extract_materials = True\n", "extract_geolocation = True\n", "coordinate_precision = 6  # Higher precision for geographic coordinates"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["All packages imported successfully\n", "\n", "ENHANCED IFC METADATA EXTRACTION (FIXED)\n", "Output directory: ../../data/processed/trino_enel/ifc_metadata\n"]}], "source": ["# Import all required packages\n", "import ifcopenshell\n", "import ifcopenshell.geom\n", "import numpy as np\n", "import pandas as pd\n", "from pyproj import Transformer, CRS\n", "import json\n", "import logging\n", "from typing import Dict, List, Optional, Tuple\n", "from collections import defaultdict\n", "import warnings\n", "from pathlib import Path\n", "import pyproj\n", "warnings.filterwarnings('ignore')\n", "    \n", "print(\"All packages imported successfully\")\n", "    \n", "# Setup\n", "logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)\n", "\n", "output_path = Path(output_dir)\n", "output_path.mkdir(parents=True, exist_ok=True)\n", "\n", "print(\"\\nENHANCED IFC METADATA EXTRACTION (FIXED)\")\n", "print(f\"Output directory: {output_path}\")"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dependencies loaded:\n", "  ifcopenshell: 0.8.2\n", "  numpy: 1.24.3\n", "  pandas: 2.3.0\n", "  pyproj: 3.7.1\n"]}], "source": ["print(\"Dependencies loaded:\")\n", "print(f\"  ifcopenshell: {ifcopenshell.__version__}\")\n", "print(f\"  numpy: {np.__version__}\")\n", "print(f\"  pandas: {pd.__version__}\")\n", "print(f\"  pyproj: {pyproj.__version__}\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. File Validation and Loading"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== IFC FILE VALIDATION ===\n", "File: GRE.EEC.S.00.IT.P.14353.00.265.ifc\n", "Size: 187.0 MB\n", "IFC file loaded successfully\n", "Schema: IFC4\n", "Entities: 2,212,652\n"]}], "source": ["from pathlib import Path\n", "import ifcopenshell\n", "\n", "def load_ifc_file(ifc_file_path: str) -> ifcopenshell.file:\n", "    print(\"=== IFC FILE VALIDATION ===\")\n", "    ifc_path = Path(ifc_file_path)\n", "\n", "    if not ifc_path.is_file():\n", "        raise FileNotFoundError(f\"IFC file not found: {ifc_path}\")\n", "\n", "    print(f\"File: {ifc_path.name}\")\n", "    print(f\"Size: {ifc_path.stat().st_size / 1024 ** 2:.1f} MB\")\n", "\n", "    try:\n", "        ifc_file = ifcopenshell.open(str(ifc_path))\n", "        print(\"IFC file loaded successfully\")\n", "        print(f\"Schema: {ifc_file.schema}\")\n", "        print(f\"Entities: {len(list(ifc_file)):,}\")\n", "        return ifc_file\n", "    except Exception as e:\n", "        raise RuntimeError(f\"Failed to load IFC file: {e}\")\n", "    \n", "ifc_file = load_ifc_file(ifc_file_path)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Helper Functions"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Helper functions ready\n"]}], "source": ["def safe_column_check(df, columns):\n", "    \"\"\"Safely check if columns exist in DataFrame.\"\"\"\n", "    if isinstance(columns, str):\n", "        columns = [columns]\n", "    return all(col in df.columns for col in columns)\n", "\n", "def safe_column_access(df, columns, default_value=None):\n", "    \"\"\"Safely access DataFrame columns, return default if missing.\"\"\"\n", "    if isinstance(columns, str):\n", "        columns = [columns]\n", "    \n", "    available_cols = [col for col in columns if col in df.columns]\n", "    \n", "    if not available_cols:\n", "        return pd.Series([default_value] * len(df))\n", "    \n", "    if len(available_cols) == 1:\n", "        return df[available_cols[0]]\n", "    else:\n", "        return df[available_cols]\n", "\n", "def extract_ifc_site_geolocation(ifc_file):\n", "    \"\"\"Extract site geolocation from IFC file.\"\"\"\n", "    sites = ifc_file.by_type(\"IfcSite\")\n", "    if not sites:\n", "        return None, None, None\n", "    \n", "    site = sites[0]\n", "    lat = getattr(site, \"RefLatitude\", None)\n", "    lon = getattr(site, \"RefLongitude\", None)\n", "    elev = getattr(site, \"RefElevation\", None)\n", "\n", "    if isinstance(lat, (tuple, list)) and lat[0] < 0:\n", "        print(\" Warning: Latitude seems to be in the southern hemisphere or malformed.\")\n", "    if isinstance(lon, (tuple, list)) and lon[0] < 0:\n", "        print(\" Warning: Longitude seems to be westward or malformed — check CRS compatibility.\")\n", "\n", "    return lat, lon, elev\n", "\n", "def convert_ifc_angle_to_decimal_degrees(ifc_angle):\n", "    \"\"\"Convert IFC DMS angle (tuple) to decimal degrees, handling negative signs correctly.\"\"\"\n", "    if ifc_angle is None:\n", "        return None\n", "\n", "    if isinstance(ifc_angle, (list, tuple)) and len(ifc_angle) >= 3:\n", "        # Handle negative degrees properly — only first component can be signed\n", "        sign = -1 if ifc_angle[0] < 0 else 1\n", "        degrees = abs(ifc_angle[0]) if ifc_angle[0] is not None else 0\n", "        minutes = abs(ifc_angle[1]) if ifc_angle[1] is not None else 0\n", "        seconds = abs(ifc_angle[2]) if ifc_angle[2] is not None else 0\n", "        microseconds = abs(ifc_angle[3]) if len(ifc_angle) > 3 and ifc_angle[3] is not None else 0\n", "        \n", "        decimal_degrees = degrees + minutes / 60.0 + seconds / 3600.0 + microseconds / 3600000000.0\n", "        return sign * decimal_degrees\n", "\n", "    if isinstance(ifc_angle, (int, float)):\n", "        return float(ifc_angle)\n", "\n", "    return None\n", "    \n", "\n", "print(\"Helper functions ready\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Site Geolocation Extraction"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== SITE GEOLOCATION EXTRACTION ===\n", " Warning: Longitude seems to be westward or malformed — check CRS compatibility.\n", "IFC metadata out of Italy range: lat=42.41486358638889, lon=-71.25807189916667\n", "Computing centroid fallback from element geometry...\n", "Local centroid: X=435986.347, Y=5011746.879\n", "Derived from geometry: lat=45.256304, lon=8.184156\n", "Final site geolocation: {'latitude': 45.25630398328059, 'longitude': 8.184155958010384, 'elevation': None}\n"]}], "source": ["# Add this near the top\n", "from pyproj import Transformer\n", "\n", "# In section ##3: Site Geolocation Extraction\n", "print(\"\\n=== SITE GEOLOCATION EXTRACTION ===\")\n", "\n", "# First, attempt to extract from IFC metadata\n", "lat_raw, lon_raw, elevation = extract_ifc_site_geolocation(ifc_file)\n", "lat_dd = convert_ifc_angle_to_decimal_degrees(lat_raw) if lat_raw else None\n", "lon_dd = convert_ifc_angle_to_decimal_degrees(lon_raw) if lon_raw else None\n", "\n", "# Determine fallback flag\n", "metadata_valid = lat_dd is not None and lon_dd is not None and (6 <= lon_dd <= 20)\n", "\n", "if metadata_valid:\n", "    print(f\"Using IFC metadata: lat={lat_dd:.6f}, lon={lon_dd:.6f}\")\n", "    site_geolocation = {'latitude': lat_dd, 'longitude': lon_dd, 'elevation': elevation}\n", "else:\n", "    if lat_dd or lon_dd:\n", "        print(f\"IFC metadata out of Italy range: lat={lat_dd}, lon={lon_dd}\")\n", "\n", "    # Fallback: compute centroid of all priority elements\n", "    print(\"Computing centroid fallback from element geometry...\")\n", "    settings = ifcopenshell.geom.settings()\n", "    settings.set(settings.USE_WORLD_COORDS, True)\n", "    file = ifcopenshell.open(ifc_file_path)\n", "    coords = []\n", "    for typ in priority_elements:\n", "        for elt in file.by_type(typ):\n", "            if hasattr(elt, 'ObjectPlacement') and elt.ObjectPlacement:\n", "                try:\n", "                    shape = ifcopenshell.geom.create_shape(settings, elt)\n", "                    verts = np.array(shape.geometry.verts).reshape(-1, 3)\n", "                    centroid = verts.mean(axis=0)\n", "                    coords.append(centroid[:2])  # X, Y\n", "                except: pass\n", "    xs, ys = map(np.array, zip(*coords))\n", "    x0, y0 = xs.mean(), ys.mean()\n", "    print(f\"Local centroid: X={x0:.3f}, Y={y0:.3f}\")\n", "\n", "    # Transform to WGS84 using EPSG:32632\n", "    transformer = Transformer.from_crs(\"EPSG:32632\", \"EPSG:4326\", always_xy=True)\n", "    lon_cent, lat_cent = transformer.transform(x0, y0)\n", "    print(f\"Derived from geometry: lat={lat_cent:.6f}, lon={lon_cent:.6f}\")\n", "    site_geolocation = {'latitude': lat_cent, 'longitude': lon_cent, 'elevation': None}\n", "\n", "print(\"Final site geolocation:\", site_geolocation)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Main Metadata Extraction"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [], "source": ["import ifcopenshell\n", "import ifcopenshell.geom\n", "import numpy as np\n", "import pandas as pd\n", "from pyproj import Transformer\n", "\n", "\n", "def count_priority_elements(ifc_file, priority_elements):\n", "    element_counts = {}\n", "    total = 0\n", "    for elem_type in priority_elements:\n", "        elements = ifc_file.by_type(elem_type)\n", "        count = len(elements)\n", "        element_counts[elem_type] = count\n", "        total += count\n", "        print(f\"{elem_type}: {count} elements\")\n", "    print(f\"\\nTotal elements to process: {total}\")\n", "    return element_counts, total\n", "\n", "\n", "def extract_element_metadata(element, settings, site_geolocation, extract_geolocation, precision, logger=None):\n", "    record = {\n", "        'GlobalId': element.GlobalId,\n", "        'Name': getattr(element, 'Name', None),\n", "        'Type': element.is_a(),\n", "        'Description': getattr(element, 'Description', None),\n", "        'Tag': getattr(element, 'Tag', None)\n", "    }\n", "\n", "    if extract_geolocation and site_geolocation:\n", "        record.update({\n", "            'Site_Latitude': site_geolocation.get('latitude'),\n", "            'Site_Longitude': site_geolocation.get('longitude'),\n", "            'Site_Elevation': site_geolocation.get('elevation')\n", "        })\n", "\n", "    try:\n", "        if hasattr(element, 'ObjectPlacement') and element.ObjectPlacement:\n", "            shape = ifcopenshell.geom.create_shape(settings, element)\n", "            verts = np.array(shape.geometry.verts).reshape(-1, 3)\n", "            centroid = verts.mean(axis=0)\n", "            x, y, z = centroid\n", "            record.update({\n", "                'X': round(x, precision),\n", "                'Y': round(y, precision),\n", "                'Z': round(z, precision),\n", "                'GeometryExtracted': True\n", "            })\n", "        else:\n", "            record.update({'X': None, 'Y': None, 'Z': None, 'GeometryExtracted': False})\n", "    except Exception as e:\n", "        if logger:\n", "            logger.warning(f\"Geometry error for {element.GlobalId}: {e}\")\n", "        record.update({'X': None, 'Y': None, 'Z': None, 'GeometryExtracted': False})\n", "\n", "    return record\n", "\n", "\n", "def detect_piles_by_name(metadata_df, pile_keywords):\n", "    mask = metadata_df['Name'].str.contains('|'.join(pile_keywords), case=False, na=False)\n", "    pile_df = metadata_df[mask]\n", "    print(f\"Piles identified by name: {len(pile_df)}\")\n", "    if not pile_df.empty:\n", "        print(\"Sample pile names:\")\n", "        print(pile_df['Name'].dropna().head(3).to_string(index=False))\n", "        valid_coords = pile_df[['X', 'Y', 'Z']].notna().all(axis=1).sum()\n", "        print(f\"Pile entries with valid coordinates: {valid_coords}\")\n", "    return pile_df\n", "\n", "\n", "def transform_coordinates(metadata_df, target_crs, precision):\n", "    if {'X', 'Y'}.issubset(metadata_df.columns) and metadata_df[['X', 'Y']].notna().any().any():\n", "        print(\"Attempting coordinate transformation to WGS84...\")\n", "        transformer = Transformer.from_crs(target_crs, \"EPSG:4326\", always_xy=True)\n", "        lons, lats = [], []\n", "        for _, row in metadata_df.iterrows():\n", "            x, y = row['X'], row['Y']\n", "            if pd.notna(x) and pd.notna(y):\n", "                try:\n", "                    lon, lat = transformer.transform(x, y)\n", "                    lons.append(round(lon, precision))\n", "                    lats.append(round(lat, precision))\n", "                except:\n", "                    lons.append(None)\n", "                    lats.append(None)\n", "            else:\n", "                lons.append(None)\n", "                lats.append(None)\n", "        metadata_df['Longitude'] = lons\n", "        metadata_df['Latitude'] = lats\n", "        print(f\"Transformed {metadata_df['Longitude'].notna().sum()} of {len(metadata_df)} coordinates to WGS84\")\n", "    else:\n", "        print(\"No valid X, Y coordinates available for transformation\")\n", "    return metadata_df\n", "\n", "\n", "def extract_and_enrich_ifc_metadata(\n", "    ifc_file_path,\n", "    priority_elements,\n", "    site_geolocation=None,\n", "    coordinate_precision=3,\n", "    extract_geolocation=True,\n", "    pile_name_keywords=None,\n", "    target_crs=\"EPSG:32643\",\n", "    logger=None\n", "):\n", "    print(\"=== MAIN METADATA EXTRACTION ===\")\n", "    ifc_file = ifcopenshell.open(ifc_file_path)\n", "    settings = ifcopenshell.geom.settings()\n", "    settings.set(settings.USE_WORLD_COORDS, True)\n", "\n", "    element_counts, total = count_priority_elements(ifc_file, priority_elements)\n", "    if total == 0:\n", "        print(\"No priority elements found\")\n", "        return pd.DataFrame()\n", "\n", "    records = []\n", "    for element_type in priority_elements:\n", "        elements = ifc_file.by_type(element_type)\n", "        print(f\"\\nProcessing {len(elements)} {element_type} elements...\")\n", "        for i, element in enumerate(elements):\n", "            try:\n", "                record = extract_element_metadata(\n", "                    element, settings, site_geolocation,\n", "                    extract_geolocation, coordinate_precision, logger\n", "                )\n", "                records.append(record)\n", "                if (i + 1) % 100 == 0:\n", "                    print(f\"Processed {i+1}/{len(elements)} {element_type} elements\")\n", "            except Exception as e:\n", "                if logger:\n", "                    logger.error(f\"Error processing {element_type} {element.GlobalId}: {e}\")\n", "                continue\n", "\n", "    metadata_df = pd.DataFrame(records)\n", "    print(f\"\\nMetadata extracted: {len(metadata_df)} records\")\n", "\n", "    if pile_name_keywords:\n", "        detect_piles_by_name(metadata_df, pile_name_keywords)\n", "\n", "    metadata_df = transform_coordinates(metadata_df, target_crs, coordinate_precision)\n", "\n", "    return metadata_df"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== MAIN METADATA EXTRACTION ===\n", "IfcColumn: 14460 elements\n", "IfcFooting: 0 elements\n", "\n", "Total elements to process: 14460\n", "\n", "Processing 14460 IfcColumn elements...\n", "Processed 100/14460 IfcColumn elements\n", "Processed 200/14460 IfcColumn elements\n", "Processed 300/14460 IfcColumn elements\n", "Processed 400/14460 IfcColumn elements\n", "Processed 500/14460 IfcColumn elements\n", "Processed 600/14460 IfcColumn elements\n", "Processed 700/14460 IfcColumn elements\n", "Processed 800/14460 IfcColumn elements\n", "Processed 900/14460 IfcColumn elements\n", "Processed 1000/14460 IfcColumn elements\n", "Processed 1100/14460 IfcColumn elements\n", "Processed 1200/14460 IfcColumn elements\n", "Processed 1300/14460 IfcColumn elements\n", "Processed 1400/14460 IfcColumn elements\n", "Processed 1500/14460 IfcColumn elements\n", "Processed 1600/14460 IfcColumn elements\n", "Processed 1700/14460 IfcColumn elements\n", "Processed 1800/14460 IfcColumn elements\n", "Processed 1900/14460 IfcColumn elements\n", "Processed 2000/14460 IfcColumn elements\n", "Processed 2100/14460 IfcColumn elements\n", "Processed 2200/14460 IfcColumn elements\n", "Processed 2300/14460 IfcColumn elements\n", "Processed 2400/14460 IfcColumn elements\n", "Processed 2500/14460 IfcColumn elements\n", "Processed 2600/14460 IfcColumn elements\n", "Processed 2700/14460 IfcColumn elements\n", "Processed 2800/14460 IfcColumn elements\n", "Processed 2900/14460 IfcColumn elements\n", "Processed 3000/14460 IfcColumn elements\n", "Processed 3100/14460 IfcColumn elements\n", "Processed 3200/14460 IfcColumn elements\n", "Processed 3300/14460 IfcColumn elements\n", "Processed 3400/14460 IfcColumn elements\n", "Processed 3500/14460 IfcColumn elements\n", "Processed 3600/14460 IfcColumn elements\n", "Processed 3700/14460 IfcColumn elements\n", "Processed 3800/14460 IfcColumn elements\n", "Processed 3900/14460 IfcColumn elements\n", "Processed 4000/14460 IfcColumn elements\n", "Processed 4100/14460 IfcColumn elements\n", "Processed 4200/14460 IfcColumn elements\n", "Processed 4300/14460 IfcColumn elements\n", "Processed 4400/14460 IfcColumn elements\n", "Processed 4500/14460 IfcColumn elements\n", "Processed 4600/14460 IfcColumn elements\n", "Processed 4700/14460 IfcColumn elements\n", "Processed 4800/14460 IfcColumn elements\n", "Processed 4900/14460 IfcColumn elements\n", "Processed 5000/14460 IfcColumn elements\n", "Processed 5100/14460 IfcColumn elements\n", "Processed 5200/14460 IfcColumn elements\n", "Processed 5300/14460 IfcColumn elements\n", "Processed 5400/14460 IfcColumn elements\n", "Processed 5500/14460 IfcColumn elements\n", "Processed 5600/14460 IfcColumn elements\n", "Processed 5700/14460 IfcColumn elements\n", "Processed 5800/14460 IfcColumn elements\n", "Processed 5900/14460 IfcColumn elements\n", "Processed 6000/14460 IfcColumn elements\n", "Processed 6100/14460 IfcColumn elements\n", "Processed 6200/14460 IfcColumn elements\n", "Processed 6300/14460 IfcColumn elements\n", "Processed 6400/14460 IfcColumn elements\n", "Processed 6500/14460 IfcColumn elements\n", "Processed 6600/14460 IfcColumn elements\n", "Processed 6700/14460 IfcColumn elements\n", "Processed 6800/14460 IfcColumn elements\n", "Processed 6900/14460 IfcColumn elements\n", "Processed 7000/14460 IfcColumn elements\n", "Processed 7100/14460 IfcColumn elements\n", "Processed 7200/14460 IfcColumn elements\n", "Processed 7300/14460 IfcColumn elements\n", "Processed 7400/14460 IfcColumn elements\n", "Processed 7500/14460 IfcColumn elements\n", "Processed 7600/14460 IfcColumn elements\n", "Processed 7700/14460 IfcColumn elements\n", "Processed 7800/14460 IfcColumn elements\n", "Processed 7900/14460 IfcColumn elements\n", "Processed 8000/14460 IfcColumn elements\n", "Processed 8100/14460 IfcColumn elements\n", "Processed 8200/14460 IfcColumn elements\n", "Processed 8300/14460 IfcColumn elements\n", "Processed 8400/14460 IfcColumn elements\n", "Processed 8500/14460 IfcColumn elements\n", "Processed 8600/14460 IfcColumn elements\n", "Processed 8700/14460 IfcColumn elements\n", "Processed 8800/14460 IfcColumn elements\n", "Processed 8900/14460 IfcColumn elements\n", "Processed 9000/14460 IfcColumn elements\n", "Processed 9100/14460 IfcColumn elements\n", "Processed 9200/14460 IfcColumn elements\n", "Processed 9300/14460 IfcColumn elements\n", "Processed 9400/14460 IfcColumn elements\n", "Processed 9500/14460 IfcColumn elements\n", "Processed 9600/14460 IfcColumn elements\n", "Processed 9700/14460 IfcColumn elements\n", "Processed 9800/14460 IfcColumn elements\n", "Processed 9900/14460 IfcColumn elements\n", "Processed 10000/14460 IfcColumn elements\n", "Processed 10100/14460 IfcColumn elements\n", "Processed 10200/14460 IfcColumn elements\n", "Processed 10300/14460 IfcColumn elements\n", "Processed 10400/14460 IfcColumn elements\n", "Processed 10500/14460 IfcColumn elements\n", "Processed 10600/14460 IfcColumn elements\n", "Processed 10700/14460 IfcColumn elements\n", "Processed 10800/14460 IfcColumn elements\n", "Processed 10900/14460 IfcColumn elements\n", "Processed 11000/14460 IfcColumn elements\n", "Processed 11100/14460 IfcColumn elements\n", "Processed 11200/14460 IfcColumn elements\n", "Processed 11300/14460 IfcColumn elements\n", "Processed 11400/14460 IfcColumn elements\n", "Processed 11500/14460 IfcColumn elements\n", "Processed 11600/14460 IfcColumn elements\n", "Processed 11700/14460 IfcColumn elements\n", "Processed 11800/14460 IfcColumn elements\n", "Processed 11900/14460 IfcColumn elements\n", "Processed 12000/14460 IfcColumn elements\n", "Processed 12100/14460 IfcColumn elements\n", "Processed 12200/14460 IfcColumn elements\n", "Processed 12300/14460 IfcColumn elements\n", "Processed 12400/14460 IfcColumn elements\n", "Processed 12500/14460 IfcColumn elements\n", "Processed 12600/14460 IfcColumn elements\n", "Processed 12700/14460 IfcColumn elements\n", "Processed 12800/14460 IfcColumn elements\n", "Processed 12900/14460 IfcColumn elements\n", "Processed 13000/14460 IfcColumn elements\n", "Processed 13100/14460 IfcColumn elements\n", "Processed 13200/14460 IfcColumn elements\n", "Processed 13300/14460 IfcColumn elements\n", "Processed 13400/14460 IfcColumn elements\n", "Processed 13500/14460 IfcColumn elements\n", "Processed 13600/14460 IfcColumn elements\n", "Processed 13700/14460 IfcColumn elements\n", "Processed 13800/14460 IfcColumn elements\n", "Processed 13900/14460 IfcColumn elements\n", "Processed 14000/14460 IfcColumn elements\n", "Processed 14100/14460 IfcColumn elements\n", "Processed 14200/14460 IfcColumn elements\n", "Processed 14300/14460 IfcColumn elements\n", "Processed 14400/14460 IfcColumn elements\n", "\n", "Processing 0 IfcFooting elements...\n", "\n", "Metadata extracted: 14460 records\n", "<PERSON><PERSON> identified by name: 14460\n", "Sample pile names:\n", "TRPL_Tracker Pile:TRPL...\n", "TRPL_Tracker Pile:TRPL...\n", "TRPL_Tracker Pile:TRPL...\n", "Pile entries with valid coordinates: 14460\n", "Attempting coordinate transformation to WGS84...\n", "Transformed 14460 of 14460 coordinates to WGS84\n"]}], "source": ["metadata_df = extract_and_enrich_ifc_metadata(\n", "    ifc_file_path=ifc_file_path,\n", "    priority_elements=[\"IfcColumn\", \"IfcFooting\"],\n", "    site_geolocation=site_geolocation, \n", "    coordinate_precision=3,\n", "    extract_geolocation=True,\n", "    pile_name_keywords=[\"pile\", \"PILE\", \"TRPL\"],\n", "    target_crs=\"EPSG:32632\"\n", ")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Data Analysis and Preview"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "def analyze_metadata_quality(metadata_df):\n", "    if len(metadata_df) == 0:\n", "        print(\"No data available for analysis\")\n", "        return\n", "\n", "    print(\"\\n=== METADATA QUALITY ANALYSIS ===\")\n", "\n", "    # Type distribution\n", "    if 'Type' in metadata_df.columns:\n", "        type_distribution = metadata_df['Type'].value_counts()\n", "        print(\"Element Type Distribution:\")\n", "        for elem_type, count in type_distribution.items():\n", "            print(f\"  {elem_type}: {count:,}\")\n", "\n", "    # Coordinate completeness (assumes X/Y/Z exist)\n", "    print(\"\\nCoordinate Data Quality:\")\n", "\n", "    local_complete = metadata_df[['X', 'Y', 'Z']].notna().all(axis=1)\n", "    print(f\"  Local (X, Y, Z): {local_complete.sum():,}/{len(metadata_df):,} ({local_complete.mean() * 100:.1f}%)\")\n", "\n", "    coords_df = metadata_df[local_complete]\n", "    if not coords_df.empty:\n", "        print(f\"    X range: {coords_df['X'].min():.3f} to {coords_df['X'].max():.3f}\")\n", "        print(f\"    Y range: {coords_df['Y'].min():.3f} to {coords_df['Y'].max():.3f}\")\n", "        print(f\"    Z range: {coords_df['Z'].min():.3f} to {coords_df['Z'].max():.3f}\")\n", "\n", "    # Geographic coordinates\n", "    if {'Latitude', 'Longitude'}.issubset(metadata_df.columns):\n", "        geo_complete = metadata_df[['Latitude', 'Longitude']].notna().all(axis=1)\n", "        print(f\"  Geographic (Lat, Lon): {geo_complete.sum():,}/{len(metadata_df):,} ({geo_complete.mean() * 100:.1f}%)\")\n", "\n", "        geo_df = metadata_df[geo_complete]\n", "        if not geo_df.empty:\n", "            print(f\"    Latitude range: {geo_df['Latitude'].min():.6f} to {geo_df['Latitude'].max():.6f}\")\n", "            print(f\"    Longitude range: {geo_df['Longitude'].min():.6f} to {geo_df['Longitude'].max():.6f}\")\n", "    else:\n", "        print(\"  Geographic coordinates not available\")\n", "\n", "    # Site geolocation based on median of transformed coordinates\n", "    if {'Latitude', 'Longitude'}.issubset(metadata_df.columns):\n", "        site_lat = metadata_df['Latitude'].median()\n", "        site_lon = metadata_df['Longitude'].median()\n", "        metadata_df['Site_Latitude'] = site_lat\n", "        metadata_df['Site_Longitude'] = site_lon\n", "        print(f\"  Site geolocation fixed to median: ({site_lat:.6f}, {site_lon:.6f})\")\n", "    else:\n", "        print(\"  Site geolocation could not be fixed — missing transformed coordinates\")\n", "\n", "\n", "\n", "    pd.set_option('display.max_columns', None)\n", "    pd.set_option('display.width', None)\n", "    pd.set_option('display.max_colwidth', 25)\n", "\n", "    # Define columns to preview (adjust as needed based on your metadata_df schema)\n", "    preview_cols = [\n", "        col for col in ['GlobalId', 'Name', 'Type', 'X', 'Y', 'Z', 'Latitude', 'Longitude']\n", "        if col in metadata_df.columns\n", "    ]\n", "\n", "    print(\"\\nFirst 10 records:\")\n", "    print(metadata_df[preview_cols].head(10).to_string(index=False, float_format='%.6f'))\n", "\n", "    print(f\"\\nDataFrame Info:\")\n", "    print(f\"  Shape: {metadata_df.shape}\")\n", "    print(f\"  Columns: {list(metadata_df.columns)}\")\n"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== METADATA QUALITY ANALYSIS ===\n", "Element Type Distribution:\n", "  IfcColumn: 14,460\n", "\n", "Coordinate Data Quality:\n", "  Local (X, Y, Z): 14,460/14,460 (100.0%)\n", "    X range: 435267.197 to 436719.949\n", "    Y range: 5010900.711 to 5012462.407\n", "    Z range: 154.992 to 159.519\n", "  Geographic (Lat, Lon): 14,460/14,460 (100.0%)\n", "    Latitude range: 45.249000 to 45.263000\n", "    Longitude range: 8.175000 to 8.193000\n", "  Site geolocation fixed to median: (45.257000, 8.184000)\n", "\n", "First 10 records:\n", "              GlobalId                                       Name      Type             X              Y          Z  Latitude  Longitude\n", "1u7AZf3On2lwsljDdawZWm TRPL_Tracker Pile:TRPL_Tracker Pile:952577 IfcColumn 435751.684000 5012179.151000 158.688000 45.260000   8.181000\n", "1u7AZf3On2lwsljDdawZWp TRPL_Tracker Pile:TRPL_Tracker Pile:952578 IfcColumn 435751.684000 5012187.444000 158.688000 45.260000   8.181000\n", "1u7AZf3On2lwsljDdawZWo TRPL_Tracker Pile:TRPL_Tracker Pile:952579 IfcColumn 435751.684000 5012195.736000 158.688000 45.260000   8.181000\n", "1u7AZf3On2lwsljDdawZWr TRPL_Tracker Pile:TRPL_Tracker Pile:952580 IfcColumn 435751.684000 5012170.859000 158.688000 45.260000   8.181000\n", "1u7AZf3On2lwsljDdawZWq TRPL_Tracker Pile:TRPL_Tracker Pile:952581 IfcColumn 435751.684000 5012204.029000 158.688000 45.260000   8.181000\n", "1u7AZf3On2lwsljDdawZWs TRPL_Tracker Pile:TRPL_Tracker Pile:952583 IfcColumn 435751.684000 5012145.338000 158.741000 45.260000   8.181000\n", "1u7AZf3On2lwsljDdawZWv TRPL_Tracker Pile:TRPL_Tracker Pile:952584 IfcColumn 435751.684000 5012153.631000 158.741000 45.260000   8.181000\n", "1u7AZf3On2lwsljDdawZWu TRPL_Tracker Pile:TRPL_Tracker Pile:952585 IfcColumn 435751.684000 5012161.923000 158.741000 45.260000   8.181000\n", "1u7AZf3On2lwsljDdawZWx TRPL_Tracker Pile:TRPL_Tracker Pile:952586 IfcColumn 435751.684000 5012137.046000 158.741000 45.260000   8.181000\n", "1u7AZf3On2lwsljDdawZWw TRPL_Tracker Pile:TRPL_Tracker Pile:952587 IfcColumn 435751.684000 5012170.216000 158.741000 45.260000   8.181000\n", "\n", "DataFrame Info:\n", "  Shape: (14460, 14)\n", "  Columns: ['GlobalId', 'Name', 'Type', 'Description', 'Tag', 'Site_Latitude', 'Site_Longitude', 'Site_Elevation', 'X', 'Y', 'Z', 'GeometryExtracted', 'Longitude', 'Latitude']\n"]}], "source": ["analyze_metadata_quality(metadata_df)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Safe Export Results"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import json\n", "from pathlib import Path\n", "\n", "def export_ifc_metadata(\n", "    metadata_df: pd.DataFrame,\n", "    output_path: Path,\n", "    ifc_file_path: str,\n", "    priority_elements: list,\n", "    target_crs: str,\n", "    site_geolocation: dict = None,\n", "    ifc_file=None\n", "):\n", "    if len(metadata_df) == 0:\n", "        print(\"No metadata to export\")\n", "        return\n", "\n", "    output_path.mkdir(parents=True, exist_ok=True)\n", "    ifc_filename = Path(ifc_file_path).stem\n", "    file_prefix = output_path / ifc_filename\n", "\n", "    print(\"\\nExporting IFC metadata:\")\n", "\n", "    # 1. Save complete metadata\n", "    full_path = file_prefix.with_name(file_prefix.name + \"_enhanced_metadata.csv\")\n", "    metadata_df.to_csv(full_path, index=False)\n", "    print(f\"- Metadata CSV: {full_path}\")\n", "\n", "    # 2. Save coordinates (local + WGS84)\n", "    coord_cols = ['GlobalId', 'Name', 'Type', 'X', 'Y', 'Z', 'Latitude', 'Longitude']\n", "    coord_df = metadata_df[[c for c in coord_cols if c in metadata_df.columns]].dropna(subset=['X', 'Y', 'Z'])\n", "    if not coord_df.empty:\n", "        coord_path = file_prefix.with_name(file_prefix.name + \"_coordinates.csv\")\n", "        coord_df.to_csv(coord_path, index=False)\n", "        print(f\"- Coordinates CSV: {coord_path}\")\n", "\n", "    # 3. Save piles if any\n", "    if 'Name' in metadata_df.columns:\n", "        pile_mask = metadata_df['Name'].str.contains('pile|PILE|TRPL', case=False, na=False)\n", "        pile_df = metadata_df[pile_mask & metadata_df[['X', 'Y', 'Z']].notna().all(axis=1)]\n", "        if not pile_df.empty:\n", "            pile_path = file_prefix.with_name(file_prefix.name + \"_piles.csv\")\n", "            pile_df.to_csv(pile_path, index=False)\n", "            print(f\"- Pile CSV: {pile_path}\")\n", "\n", "    # 4. Write summary JSON\n", "    summary = {\n", "        \"ifc_file\": Path(ifc_file_path).name,\n", "        \"schema\": getattr(ifc_file, \"schema\", \"Unknown\"),\n", "        \"elements\": len(metadata_df),\n", "        \"priority_types\": priority_elements,\n", "        \"target_crs\": target_crs,\n", "        \"site_geolocation\": site_geolocation or {},\n", "        \"type_distribution\": metadata_df['Type'].value_counts().to_dict() if 'Type' in metadata_df.columns else {},\n", "        \"columns\": list(metadata_df.columns)\n", "    }\n", "    json_path = file_prefix.with_name(file_prefix.name + \"_summary.json\")\n", "    with open(json_path, \"w\") as f:\n", "        json.dump(summary, f, indent=2)\n", "    print(f\"- Summary JSON: {json_path}\")"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["../../../data/processed/trino_enel/ifc_metadata\n"]}], "source": ["output_path = Path(\"../../../data/processed/trino_enel/ifc_metadata\")\n", "print(output_path)\n"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["total 0\n"]}], "source": ["!ls -lh ../../../data/processed/trino_enel/ifc_metadata"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Exporting IFC metadata:\n", "- Metadata CSV: ../../../data/processed/trino_enel/ifc_metadata/GRE.EEC.S.00.IT.P.14353.00.265_enhanced_metadata.csv\n", "- Coordinates CSV: ../../../data/processed/trino_enel/ifc_metadata/GRE.EEC.S.00.IT.P.14353.00.265_coordinates.csv\n", "- Pile CSV: ../../../data/processed/trino_enel/ifc_metadata/GRE.EEC.S.00.IT.P.14353.00.265_piles.csv\n", "- Summary JSON: ../../../data/processed/trino_enel/ifc_metadata/GRE.EEC.S.00.IT.P.14353.00.265_summary.json\n"]}], "source": ["export_ifc_metadata(\n", "    metadata_df=metadata_df,\n", "    output_path=output_path,\n", "    ifc_file_path=ifc_file_path,  \n", "    priority_elements=priority_elements,  \n", "    target_crs=target_crs,  \n", "    site_geolocation=site_geolocation,  \n", "    ifc_file=ifc_file \n", ")\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}