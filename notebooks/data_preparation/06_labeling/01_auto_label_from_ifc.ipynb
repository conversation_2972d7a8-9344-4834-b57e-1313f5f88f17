# Import required libraries
import numpy as np
import pandas as pd
import open3d as o3d
import matplotlib.pyplot as plt
from pathlib import Path
import json
from scipy.spatial import cKDTree
from scipy.spatial.distance import cdist
import warnings
warnings.filterwarnings('ignore')
import logging
from datetime import datetime
import os
import random
from sklearn.model_selection import train_test_split

# Papermill parameters cell
site_name = "trino_enel"
project_type = "ENEL"
ground_segmentation_method = "csf"  # Options: "csf", "pmf", "ransac", "ransac_pmf"

# Search radius for XY-only patch extraction
search_radius=10.0

# Training patch parameters
patch_radius = 2.0  # Radius around pile center for patch extraction (meters)
min_points_per_patch = 50  # Minimum points required for a valid training patch
max_points_per_patch = 2048  # Maximum points per patch (for PointNet++ compatibility)
negative_sample_ratio = 2.0  # Ratio of negative to positive samples

# Set up paths
base_path = Path('../../..')  # Adjust to your project root
data_path = base_path / 'data'
processed_path = data_path / 'processed' / site_name
output_path = data_path / 'output_runs' / 'labeled_training_data' / f"{site_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
output_path.mkdir(parents=True, exist_ok=True)

# Data quality parameters
z_filter_min = 0.5  # Minimum height above ground for pile detection (meters)
z_filter_max = 4.0   # Maximum height above ground for pile detection (meters)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

logger.info("Auto-labeling from IFC for ML Training - Ready!")
logger.info(f"Site: {site_name}")
logger.info(f"Ground segmentation method: {ground_segmentation_method}")
logger.info(f"Data path: {data_path}")
logger.info(f"Output path: {output_path}")
logger.info(f"Patch radius: {patch_radius}m")
logger.info(f"Min points per patch: {min_points_per_patch}")
logger.info(f"Max points per patch: {max_points_per_patch}")

# Load ground-segmented point cloud data
ground_seg_path = processed_path / 'ground_segmentation' / ground_segmentation_method
nonground_file = ground_seg_path / f"{site_name}_nonground.ply"

try:
    # Load non-ground points (structures, vegetation, piles)
    pcd = o3d.io.read_point_cloud(str(nonground_file))
    points = np.asarray(pcd.points)
    
    logger.info(f"Loaded ground-segmented point cloud: {len(points):,} non-ground points")
    logger.info(f"Source file: {nonground_file}")
    logger.info(f"Point cloud bounds:")
    logger.info(f"  - X: [{points[:, 0].min():.2f}, {points[:, 0].max():.2f}]")
    logger.info(f"  - Y: [{points[:, 1].min():.2f}, {points[:, 1].max():.2f}]")
    logger.info(f"  - Z: [{points[:, 2].min():.2f}, {points[:, 2].max():.2f}]")
    
    # Check if colors are available
    if pcd.has_colors():
        colors = np.asarray(pcd.colors)
        logger.info(f"Point cloud has RGB colors")
    else:
        colors = None
        logger.info(f"Point cloud has no color information")
        
except FileNotFoundError:
    logger.error(f"Ground-segmented point cloud file not found: {nonground_file}")
    logger.error("Please run ground segmentation processing first.")
    raise

# Load IFC pile metadata
ifc_metadata_path = processed_path / 'ifc_metadata' / 'GRE.EEC.S.00.IT.P.14353.00.265_piles.csv'

try:
    ifc_metadata = pd.read_csv(ifc_metadata_path)
    logger.info(f"Loaded IFC pile metadata: {len(ifc_metadata)} piles")
    logger.info(f"Columns: {list(ifc_metadata.columns)}")
    
    # Display sample data
    logger.info("Sample IFC pile metadata:")
    print(ifc_metadata.head())
    
    # Extract pile numbers from Tag column
    if 'Tag' in ifc_metadata.columns:
        pile_numbers = ifc_metadata['Tag'].unique()
        logger.info(f"Found {len(pile_numbers)} unique pile numbers")
        logger.info(f"Pile number range: {pile_numbers.min()} to {pile_numbers.max()}")
    
    # Check coordinate ranges
    coord_cols = ['X', 'Y', 'Z']
    if all(col in ifc_metadata.columns for col in coord_cols):
        logger.info(f"IFC Coordinate Ranges:")
        for col in coord_cols:
            min_val, max_val = ifc_metadata[col].min(), ifc_metadata[col].max()
            logger.info(f"  - {col}: [{min_val:.2f}, {max_val:.2f}]")
    
except FileNotFoundError:
    logger.error(f"IFC metadata file not found: {ifc_metadata_path}")
    logger.error("Please run IFC metadata extraction first.")
    raise

# Validate IFC metadata
# Extract pile coordinates from IFC metadata
coord_cols = ['X', 'Y', 'Z']
if not all(col in ifc_metadata.columns for col in coord_cols):
    raise ValueError(f"IFC metadata must contain columns: {coord_cols}")
    
pile_coords = ifc_metadata[coord_cols].values
pile_numbers = ifc_metadata['Tag'].values if 'Tag' in ifc_metadata.columns else np.arange(len(pile_coords))
print(pile_coords.shape)
print(pile_numbers.shape)

def detect_coordinate_mismatch(points, pile_coords, threshold=50.0):
    # Analyze Z-coordinates
    drone_z_range = points[:, 2].max() - points[:, 2].min()
    ifc_z_range = pile_coords[:, 2].max() - pile_coords[:, 2].min()
    
    drone_z_mean = points[:, 2].mean()
    ifc_z_mean = pile_coords[:, 2].mean()
    
    z_separation = abs(drone_z_mean - ifc_z_mean)
    
    logger.info(f"Z-Coordinate Analysis:")
    logger.info(f"  Drone Z: {points[:, 2].min():.1f} - {points[:, 2].max():.1f} (range: {drone_z_range:.1f}m)")
    logger.info(f"  IFC Z:   {pile_coords[:, 2].min():.1f} - {pile_coords[:, 2].max():.1f} (range: {ifc_z_range:.1f}m)")
    logger.info(f"  Mean separation: {z_separation:.1f}m")
    
    has_mismatch = z_separation > threshold
    z_offset = ifc_z_mean - drone_z_mean
    
    if has_mismatch:
        logger.info(f"DETECTED: Major Z-coordinate mismatch (likely absolute vs relative)")
    else:
        logger.info(f"Z-coordinates appear to be in same reference system")
    
    return has_mismatch, z_offset

def extract_pile_patch_xy_only(points, pile_center, radius, min_points=50, max_points=2048):
    # Calculate distances to pile center (only X, Y for cylindrical search)
    distances_2d = np.sqrt((points[:, 0] - pile_center[0])**2 + 
                          (points[:, 1] - pile_center[1])**2)
    
    # Find points within radius
    patch_mask = distances_2d <= radius
    patch_indices = np.where(patch_mask)[0]
    patch_points = points[patch_mask]
    
    # Check if patch has enough points
    is_valid = len(patch_points) >= min_points
    
    if is_valid and len(patch_points) > max_points:
        # Randomly sample to max_points
        sample_indices = np.random.choice(len(patch_points), max_points, replace=False)
        patch_points = patch_points[sample_indices]
        patch_indices = patch_indices[sample_indices]
    
    # Convert to relative coordinates (centered on pile XY, keep original Z)
    if is_valid:
        # Use drone point cloud's median Z as reference instead of IFC Z
        pile_center_corrected = np.array([pile_center[0], pile_center[1], np.median(patch_points[:, 2])])
        patch_points_relative = patch_points - pile_center_corrected
    else:
        patch_points_relative = patch_points
    
    return patch_points_relative, patch_indices, is_valid

# Detect Z-coordinate mismatch
has_z_mismatch, z_offset = detect_coordinate_mismatch(points, pile_coords)
print(f"Z offset: {z_offset:.2f}m")

if has_z_mismatch:
    logger.info("Using XY-only labeling to handle Z-coordinate mismatch")
    logger.info(f"Using specified search radius: {patch_radius}m")
else:
    logger.info("Using full XYZ labeling with compatible coordinate systems")

extract_function = extract_pile_patch_xy_only  # Still use XY-only for consistency
print (extract_function.__name__)

def create_xy_only_labeling(drone_points, pile_coords, search_radius=4.0, min_points=15):
    """Label using XY coordinates only, ignoring Z differences"""
    
    print(f"XY-only labeling with {search_radius}m search radius...")
    print("Ignoring Z-coordinates due to coordinate system mismatch")
    
    # Use only XY coordinates for spatial search
    drone_xy = drone_points[:, :2]
    pile_xy = pile_coords[:, :2]
    
    # Build KD-tree for drone XY points
    tree = cKDTree(drone_xy)
    
    # Initialize labels
    labels = np.zeros(len(drone_points), dtype=int)
    pile_assignments = np.full(len(drone_points), -1, dtype=int)
    
    labeled_piles = 0
    total_pile_points = 0
    pile_details = []
    
    print(f"Searching for points around {len(pile_coords)} pile XY locations...")
    
    for i, pile_coord in enumerate(pile_coords):
        pile_xy_coord = pile_coord[:2]  # Use only X,Y
        
        # Find drone points within XY search radius
        indices = tree.query_ball_point(pile_xy_coord, search_radius)
        
        if len(indices) >= min_points:
            # Label these points as pile
            labels[indices] = 1
            pile_assignments[indices] = i
            
            labeled_piles += 1
            total_pile_points += len(indices)
            
            # Calculate XY distances for quality assessment
            xy_distances = np.linalg.norm(drone_xy[indices] - pile_xy_coord, axis=1)
            mean_xy_distance = np.mean(xy_distances)
            
            pile_details.append({
                'pile_id': i,
                'xy_coordinates': pile_xy_coord.tolist(),
                'point_count': len(indices),
                'mean_xy_distance': mean_xy_distance,
                'search_radius': search_radius
            })
            
            if labeled_piles <= 5:  # Show details for first few piles
                print(f"  Pile {i}: {len(indices)} points, avg XY distance {mean_xy_distance:.1f}m")
        
        if (i + 1) % 1000 == 0:  # Progress updates
            print(f"  Processed {i + 1}/{len(pile_coords)} piles")
    
    detection_rate = labeled_piles / len(pile_coords) * 100
    
    print(f"XY-only labeling complete:")
    print(f"  Labeled piles: {labeled_piles}/{len(pile_coords)} ({detection_rate:.1f}%)")
    print(f"  Pile points: {total_pile_points:,}")
    print(f"  Background points: {len(drone_points) - total_pile_points:,}")
    print(f"  Pile coverage: {total_pile_points/len(drone_points)*100:.1f}% of total points")
    
    return labels, pile_assignments, {
        'total_piles': len(pile_coords),
        'labeled_piles': labeled_piles,
        'total_pile_points': total_pile_points,
        'detection_rate': detection_rate,
        'pile_details': pile_details,
        'search_radius_used': search_radius,
        'method': 'xy_only_labeling'
    }

def generate_negative_samples(points, pile_coords, patch_radius, num_negative_samples, min_distance=5.0):
    # Generate negative training samples (non-pile patches) from the point cloud.
    
    # Get point cloud bounds
    x_min, x_max = points[:, 0].min(), points[:, 0].max()
    y_min, y_max = points[:, 1].min(), points[:, 1].max()
    
    negative_centers = []
    attempts = 0
    max_attempts = num_negative_samples * 10  # Prevent infinite loop
    
    while len(negative_centers) < num_negative_samples and attempts < max_attempts:
        # Random location within point cloud bounds
        candidate_x = np.random.uniform(x_min + patch_radius, x_max - patch_radius)
        candidate_y = np.random.uniform(y_min + patch_radius, y_max - patch_radius)
        candidate_z = np.median(points[:, 2])  # Use median Z as reference
        
        candidate_center = np.array([candidate_x, candidate_y, candidate_z])
        
        # Check distance to all pile centers
        distances_to_piles = np.sqrt(np.sum((pile_coords[:, :2] - candidate_center[:2])**2, axis=1))
        
        if np.min(distances_to_piles) >= min_distance:
            negative_centers.append(candidate_center)
        
        attempts += 1
    
    logger.info(f"Generated {len(negative_centers)} negative samples in {attempts} attempts")
    return negative_centers

# Use your improved XY-only labeling function
labels, pile_assignments, pile_info = create_xy_only_labeling(
    drone_points=points,
    pile_coords=pile_coords,
    search_radius=search_radius,  # Use the parameter from the top
    min_points=15
)

# Convert to training patches format for compatibility with rest of notebook
training_patches = []
valid_pile_count = pile_info['labeled_piles']

# Create training patches from labeled points
for pile_detail in pile_info['pile_details']:
    pile_id = pile_detail['pile_id']
    pile_mask = pile_assignments == pile_id
    pile_points = points[pile_mask]
    
    if len(pile_points) > 0:
        # Center points relative to pile center
        pile_center = pile_coords[pile_id]
        pile_center_corrected = np.array([pile_center[0], pile_center[1], np.median(pile_points[:, 2])])
        pile_points_relative = pile_points - pile_center_corrected
        
        patch_data = {
            'points': pile_points_relative,
            'label': 1,
            'pile_number': pile_numbers[pile_id],
            'center': pile_center,
            'patch_type': 'positive',
            'point_count': len(pile_points)
        }
        training_patches.append(patch_data)

logger.info(f"Converted to {len(training_patches)} training patches")


# Negative samples
negative_ratio = 2.0
min_points = 15
max_points = 2048

num_negative_samples = int(valid_pile_count * negative_ratio)
logger.info(f"Creating {num_negative_samples} negative training patches...")

negative_centers = generate_negative_samples(
    points, pile_coords, patch_radius, num_negative_samples
)

for i, neg_center in enumerate(negative_centers):
    patch_points, patch_indices, is_valid = extract_function(
        points, neg_center, patch_radius, min_points, max_points
    )
    
    if is_valid:
        patch_data = {
            'points': patch_points,
            'label': 0,
            'pile_number': -1,
            'center': neg_center,
            'patch_type': 'negative',
            'point_count': len(patch_points)
        }
        training_patches.append(patch_data)

# Stats
total_pile_points = sum(p['point_count'] for p in training_patches if p['label'] == 1)
total_background_points = len(points) - total_pile_points
pile_coverage = total_pile_points / len(points) * 100

logger.info(f"  Pile points: {total_pile_points:,}")
logger.info(f"  Background points: {total_background_points:,}")
logger.info(f"  Pile coverage: {pile_coverage:.1f}% of total points")
logger.info(f"Created {len(training_patches)} total training patches")
logger.info(f"  - Positive patches: {valid_pile_count}")
logger.info(f"  - Negative patches: {len(training_patches) - valid_pile_count}")


def visualize_training_patches(training_patches, sample_size=10):
    """
    Visualize sample training patches.
    """
    # Separate positive and negative patches
    positive_patches = [p for p in training_patches if p['label'] == 1]
    negative_patches = [p for p in training_patches if p['label'] == 0]
    
    # Sample patches for visualization
    pos_sample = random.sample(positive_patches, min(sample_size//2, len(positive_patches)))
    neg_sample = random.sample(negative_patches, min(sample_size//2, len(negative_patches)))
    
    fig = plt.figure(figsize=(20, 12))
    
    # Plot positive patches
    for i, patch in enumerate(pos_sample):
        ax = fig.add_subplot(2, sample_size//2, i+1, projection='3d')
        points = patch['points']
        ax.scatter(points[:, 0], points[:, 1], points[:, 2], 
                  c='red', s=1, alpha=0.6)
        ax.set_title(f"Pile {patch['pile_number']}\n{len(points)} points")
        ax.set_xlabel('X (m)')
        ax.set_ylabel('Y (m)')
        ax.set_zlabel('Z (m)')
    
    # Plot negative patches
    for i, patch in enumerate(neg_sample):
        ax = fig.add_subplot(2, sample_size//2, sample_size//2 + i+1, projection='3d')
        points = patch['points']
        ax.scatter(points[:, 0], points[:, 1], points[:, 2], 
                  c='gray', s=1, alpha=0.6)
        ax.set_title(f"Non-pile\n{len(points)} points")
        ax.set_xlabel('X (m)')
        ax.set_ylabel('Y (m)')
        ax.set_zlabel('Z (m)')
    
    plt.tight_layout()
    plt.show()
    
    # Print statistics
    logger.info(f"Training Patch Statistics:")
    logger.info(f"  Total patches: {len(training_patches)}")
    logger.info(f"  Positive patches: {len(positive_patches)}")
    logger.info(f"  Negative patches: {len(negative_patches)}")
    
    if positive_patches:
        pos_points = [p['point_count'] for p in positive_patches]
        logger.info(f"  Positive patch points: mean={np.mean(pos_points):.1f}, std={np.std(pos_points):.1f}")
    
    if negative_patches:
        neg_points = [p['point_count'] for p in negative_patches]
        logger.info(f"  Negative patch points: mean={np.mean(neg_points):.1f}, std={np.std(neg_points):.1f}")

visualize_training_patches(training_patches, sample_size=8)

# Create training patches with Z-coordinate correction
if 'points' in locals() and 'ifc_metadata' in locals():
    
    logger.info(f"=== Z-COORDINATE CORRECTED LABELING ===")
    logger.info(f"Handling absolute vs relative Z-coordinate systems")
    
    logger.info(f"Creating training patches with parameters:")
    logger.info(f"  - Patch radius: {patch_radius}m")
    logger.info(f"  - Min points per patch: {min_points_per_patch}")
    logger.info(f"  - Max points per patch: {max_points_per_patch}")
    logger.info(f"  - Negative sample ratio: {negative_sample_ratio}")
    
    # Create training patches with Z-coordinate correction
    training_patches = create_training_patches_z_corrected(
        points=points,
        ifc_metadata=ifc_metadata,
        patch_radius=patch_radius,
        min_points=min_points_per_patch,
        max_points=max_points_per_patch,
        negative_ratio=negative_sample_ratio
    )
    
    # Visualize sample patches
    if len(training_patches) > 0:
        visualize_training_patches(training_patches, sample_size=8)
    else:
        logger.warning("No training patches created. Check coordinate systems and parameters.")
    
else:
    logger.error("Please ensure both point cloud and IFC metadata are loaded.")

from datetime import datetime
from sklearn.model_selection import train_test_split
import numpy as np
import json

def save_labeled_data(training_patches, output_path, site_name, ground_seg_method):
    """
    Save training patches individually, as a combined dataset, and in train/val splits.
    """
    # Setup
    patches_dir = output_path / 'patches'
    patches_dir.mkdir(exist_ok=True)
    
    pos_patches = [p for p in training_patches if p['label'] == 1]
    neg_patches = [p for p in training_patches if p['label'] == 0]

    def save_patch_file(patch, prefix, index):
        fname = f"{prefix}_{patch.get('pile_number', index):06d}_{index:04d}.npz" if prefix == "pile" else f"{prefix}_{index:06d}.npz"
        np.savez_compressed(
            patches_dir / fname,
            points=patch['points'],
            label=patch['label'],
            pile_number=patch['pile_number'],
            center=patch['center'],
            patch_type=patch['patch_type']
        )

    logger.info("Saving individual training patches...")
    for i, p in enumerate(pos_patches): save_patch_file(p, "pile", i)
    for i, p in enumerate(neg_patches): save_patch_file(p, "nonpile", i)
    logger.info(f"Saved {len(training_patches)} individual patch files")

    # Combine all points
    all_points, all_labels, all_pile_ids, patch_indices = [], [], [], []
    idx = 0
    for patch in training_patches:
        pts = patch['points']
        n = len(pts)
        all_points.append(pts)
        all_labels.extend([patch['label']] * n)
        all_pile_ids.extend([patch['pile_number']] * n)
        patch_indices.append((idx, idx + n))
        idx += n

    combined_points = np.vstack(all_points)
    combined_data = {
        'points': combined_points,
        'labels': np.array(all_labels),
        'pile_numbers': np.array(all_pile_ids),
        'patch_indices': patch_indices
    }
    combined_file = output_path / f"{site_name}_{ground_seg_method}_training_dataset.npz"
    np.savez_compressed(combined_file, **combined_data)
    logger.info(f"Saved combined training dataset: {combined_file}")

    # Train/val split
    train, val = train_test_split(
        training_patches, test_size=0.2, random_state=42,
        stratify=[p['label'] for p in training_patches]
    )

    def save_split(split, name):
        points = np.vstack([p['points'] for p in split])
        labels = np.hstack([[p['label']] * len(p['points']) for p in split])
        pile_ids = np.hstack([[p['pile_number']] * len(p['points']) for p in split])
        np.savez_compressed(output_path / f"{site_name}_{ground_seg_method}_{name}.npz",
                            points=points, labels=labels, pile_numbers=pile_ids)

    save_split(train, "train")
    save_split(val, "val")
    logger.info(f"Saved train/validation splits: {len(train)}/{len(val)} patches")

    # Metadata
    metadata = {
        'site_name': site_name,
        'ground_segmentation_method': ground_seg_method,
        'creation_timestamp': datetime.now().isoformat(),
        'total_patches': len(training_patches),
        'positive_patches': len(pos_patches),
        'negative_patches': len(neg_patches),
        'train_patches': len(train),
        'val_patches': len(val),
        'total_points': len(combined_points),
        'parameters': {
            'patch_radius': patch_radius,
            'min_points_per_patch': min_points_per_patch,
            'max_points_per_patch': max_points_per_patch,
            'negative_sample_ratio': negative_sample_ratio,
            'z_filter_min': z_filter_min,
            'z_filter_max': z_filter_max
        },
        'class_distribution': {
            'positive_ratio': len(pos_patches) / len(training_patches),
            'negative_ratio': len(neg_patches) / len(training_patches)
        }
    }
    
    metadata_file = output_path / f"{site_name}_{ground_seg_method}_metadata.json"
    with open(metadata_file, 'w') as f:
        json.dump(metadata, f, indent=2)
    logger.info(f"Saved metadata: {metadata_file}")

    return {
        'patches_dir': patches_dir,
        'combined_file': combined_file,
        'train_file': output_path / f"{site_name}_{ground_seg_method}_train.npz",
        'val_file': output_path / f"{site_name}_{ground_seg_method}_val.npz",
        'metadata_file': metadata_file
    }


# Save the training data
if 'training_patches' in locals():
    
    logger.info("Saving labeled training data...")
    
    saved_files = save_labeled_data(
        training_patches=training_patches,
        output_path=output_path,
        site_name=site_name,
        ground_seg_method=ground_segmentation_method
    )
    
    logger.info(f"Export complete! Files saved to: {output_path}")
    logger.info(f"Saved files:")
    for file_type, file_path in saved_files.items():
        logger.info(f"  - {file_type}: {file_path}")
        
else:
    logger.error("No training patches to export. Please run the patch creation process first.")

def assess_training_quality(training_patches, ifc_metadata):
    """
    Assess the quality of generated training patches.
    """
    
    logger.info("Training Data Quality Assessment:")
    logger.info("=" * 50)
    
    # Separate positive and negative patches
    positive_patches = [p for p in training_patches if p['label'] == 1]
    negative_patches = [p for p in training_patches if p['label'] == 0]
    
    # Basic statistics
    total_patches = len(training_patches)
    total_ifc_piles = len(ifc_metadata)
    
    logger.info(f"Patch Distribution:")
    logger.info(f"  Total patches: {total_patches:,}")
    logger.info(f"  Positive patches (piles): {len(positive_patches):,}")
    logger.info(f"  Negative patches (non-piles): {len(negative_patches):,}")
    logger.info(f"  Class balance: {len(positive_patches)/total_patches*100:.1f}% positive")
    
    # Pile coverage
    coverage_rate = len(positive_patches) / total_ifc_piles if total_ifc_piles > 0 else 0
    logger.info(f"Pile Coverage:")
    logger.info(f"  Total piles in IFC: {total_ifc_piles:,}")
    logger.info(f"  Successfully extracted: {len(positive_patches):,}")
    logger.info(f"  Coverage rate: {coverage_rate*100:.1f}%")
    
    # Points per patch statistics
    if positive_patches:
        pos_points = [p['point_count'] for p in positive_patches]
        logger.info(f"Positive Patch Statistics:")
        logger.info(f"  Mean points: {np.mean(pos_points):.1f}")
        logger.info(f"  Median points: {np.median(pos_points):.1f}")
        logger.info(f"  Min points: {np.min(pos_points)}")
        logger.info(f"  Max points: {np.max(pos_points)}")
        logger.info(f"  Std points: {np.std(pos_points):.1f}")
    
    if negative_patches:
        neg_points = [p['point_count'] for p in negative_patches]
        logger.info(f"Negative Patch Statistics:")
        logger.info(f"  Mean points: {np.mean(neg_points):.1f}")
        logger.info(f"  Median points: {np.median(neg_points):.1f}")
        logger.info(f"  Min points: {np.min(neg_points)}")
        logger.info(f"  Max points: {np.max(neg_points)}")
        logger.info(f"  Std points: {np.std(neg_points):.1f}")
    
    # Quality indicators
    logger.info(f"Quality Indicators:")
    logger.info("=" * 50)
    
    if coverage_rate >= 0.8:
        logger.info(f"  Excellent pile coverage ({coverage_rate*100:.1f}%)")
    elif coverage_rate >= 0.6:
        logger.info(f"  Good pile coverage ({coverage_rate*100:.1f}%)")
    else:
        logger.info(f"  Low pile coverage ({coverage_rate*100:.1f}%) - consider adjusting parameters")
    
    class_balance = len(positive_patches) / total_patches
    if 0.2 <= class_balance <= 0.8:
        logger.info(f"  Good class balance ({class_balance*100:.1f}% positive)")
    else:
        logger.info(f"  Class imbalance detected ({class_balance*100:.1f}% positive)")
    
    if positive_patches and np.mean(pos_points) >= min_points_per_patch:
        logger.info(f"  Sufficient points per patch (avg: {np.mean(pos_points):.1f})")
    else:
        logger.info(f"  Low points per patch - consider increasing patch radius")
    
    return {
        'coverage_rate': coverage_rate,
        'class_balance': class_balance,
        'avg_points_per_positive_patch': np.mean(pos_points) if positive_patches else 0,
        'avg_points_per_negative_patch': np.mean(neg_points) if negative_patches else 0
    }

# Assess training data quality
if 'training_patches' in locals():
    quality_metrics = assess_training_quality(training_patches, ifc_metadata)
else:
    logger.error("No training patches to assess. Please run the patch creation process first.")