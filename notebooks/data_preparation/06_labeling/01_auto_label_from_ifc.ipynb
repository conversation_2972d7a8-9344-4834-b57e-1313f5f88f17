# Import required libraries
import numpy as np
import pandas as pd
import open3d as o3d
import matplotlib.pyplot as plt
from pathlib import Path
import json
from scipy.spatial import cKDTree
from scipy.spatial.distance import cdist
import warnings
warnings.filterwarnings('ignore')
import logging
from datetime import datetime
import os
import random
from sklearn.model_selection import train_test_split

# Papermill parameters cell
site_name = "trino_enel"
project_type = "ENEL"
ground_segmentation_method = "csf"  # Options: "csf", "pmf", "ransac", "ransac_pmf"

# Training patch parameters
patch_radius = 2.0  # Radius around pile center for patch extraction (meters)
min_points_per_patch = 50  # Minimum points required for a valid training patch
max_points_per_patch = 2048  # Maximum points per patch (for PointNet++ compatibility)
negative_sample_ratio = 2.0  # Ratio of negative to positive samples

# Set up paths
base_path = Path('../../..')  # Adjust to your project root
data_path = base_path / 'data'
processed_path = data_path / 'processed' / site_name
output_path = data_path / 'output_runs' / 'labeled_training_data' / f"{site_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
output_path.mkdir(parents=True, exist_ok=True)

# Data quality parameters
z_filter_min = 0.5  # Minimum height above ground for pile detection (meters)
z_filter_max = 4.0   # Maximum height above ground for pile detection (meters)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

logger.info("Auto-labeling from IFC for ML Training - Ready!")
logger.info(f"Site: {site_name}")
logger.info(f"Ground segmentation method: {ground_segmentation_method}")
logger.info(f"Data path: {data_path}")
logger.info(f"Output path: {output_path}")
logger.info(f"Patch radius: {patch_radius}m")
logger.info(f"Min points per patch: {min_points_per_patch}")
logger.info(f"Max points per patch: {max_points_per_patch}")

# Load ground-segmented point cloud data
ground_seg_path = processed_path / 'ground_segmentation' / ground_segmentation_method
nonground_file = ground_seg_path / f"{site_name}_nonground.ply"

try:
    # Load non-ground points (structures, vegetation, piles)
    pcd = o3d.io.read_point_cloud(str(nonground_file))
    points = np.asarray(pcd.points)
    
    logger.info(f"Loaded ground-segmented point cloud: {len(points):,} non-ground points")
    logger.info(f"Source file: {nonground_file}")
    logger.info(f"Point cloud bounds:")
    logger.info(f"  - X: [{points[:, 0].min():.2f}, {points[:, 0].max():.2f}]")
    logger.info(f"  - Y: [{points[:, 1].min():.2f}, {points[:, 1].max():.2f}]")
    logger.info(f"  - Z: [{points[:, 2].min():.2f}, {points[:, 2].max():.2f}]")
    
    # Check if colors are available
    if pcd.has_colors():
        colors = np.asarray(pcd.colors)
        logger.info(f"Point cloud has RGB colors")
    else:
        colors = None
        logger.info(f"Point cloud has no color information")
        
except FileNotFoundError:
    logger.error(f"Ground-segmented point cloud file not found: {nonground_file}")
    logger.error("Please run ground segmentation processing first.")
    raise

# Load IFC pile metadata
ifc_metadata_path = processed_path / 'ifc_metadata' / 'GRE.EEC.S.00.IT.P.14353.00.265_piles.csv'

try:
    ifc_metadata = pd.read_csv(ifc_metadata_path)
    logger.info(f"Loaded IFC pile metadata: {len(ifc_metadata)} piles")
    logger.info(f"Columns: {list(ifc_metadata.columns)}")
    
    # Display sample data
    logger.info("Sample IFC pile metadata:")
    print(ifc_metadata.head())
    
    # Extract pile numbers from Tag column
    if 'Tag' in ifc_metadata.columns:
        pile_numbers = ifc_metadata['Tag'].unique()
        logger.info(f"Found {len(pile_numbers)} unique pile numbers")
        logger.info(f"Pile number range: {pile_numbers.min()} to {pile_numbers.max()}")
    
    # Check coordinate ranges
    coord_cols = ['X', 'Y', 'Z']
    if all(col in ifc_metadata.columns for col in coord_cols):
        logger.info(f"IFC Coordinate Ranges:")
        for col in coord_cols:
            min_val, max_val = ifc_metadata[col].min(), ifc_metadata[col].max()
            logger.info(f"  - {col}: [{min_val:.2f}, {max_val:.2f}]")
    
except FileNotFoundError:
    logger.error(f"IFC metadata file not found: {ifc_metadata_path}")
    logger.error("Please run IFC metadata extraction first.")
    raise

def detect_coordinate_mismatch(points, pile_coords, threshold=50.0):
    """
    Detect Z-coordinate mismatch between drone and IFC data.
    
    Args:
        points: Drone point cloud array (N, 3)
        pile_coords: IFC pile coordinates (M, 3)
        threshold: Threshold for detecting major Z-coordinate mismatch (meters)
    
    Returns:
        has_mismatch: Boolean indicating if major Z-coordinate mismatch detected
        z_offset: Estimated Z-offset between coordinate systems
    """
    drone_z_range = points[:, 2].max() - points[:, 2].min()
    ifc_z_range = pile_coords[:, 2].max() - pile_coords[:, 2].min()
    
    drone_z_mean = points[:, 2].mean()
    ifc_z_mean = pile_coords[:, 2].mean()
    
    z_separation = abs(drone_z_mean - ifc_z_mean)
    
    logger.info(f"Z-Coordinate Analysis:")
    logger.info(f"  Drone Z: {points[:, 2].min():.1f} - {points[:, 2].max():.1f} (range: {drone_z_range:.1f}m)")
    logger.info(f"  IFC Z:   {pile_coords[:, 2].min():.1f} - {pile_coords[:, 2].max():.1f} (range: {ifc_z_range:.1f}m)")
    logger.info(f"  Mean separation: {z_separation:.1f}m")
    
    has_mismatch = z_separation > threshold
    z_offset = ifc_z_mean - drone_z_mean
    
    if has_mismatch:
        logger.info(f"  🔍 DETECTED: Major Z-coordinate mismatch (likely absolute vs relative)")
    else:
        logger.info(f"  ✓ Z-coordinates appear to be in same reference system")
    
    return has_mismatch, z_offset

def extract_pile_patch_xy_only(points, pile_center, radius, min_points=50, max_points=2048):
    """
    Extract a training patch around a pile location using XY-only search.
    This handles Z-coordinate mismatches between drone and IFC data.
    
    Args:
        points: Point cloud array (N, 3)
        pile_center: Pile center coordinates (x, y, z) - only X,Y used
        radius: Search radius in meters
        min_points: Minimum points required for valid patch
        max_points: Maximum points per patch (for PointNet++ compatibility)
    
    Returns:
        patch_points: Points in the patch (relative to pile center)
        patch_indices: Original indices of points in the patch
        is_valid: Whether patch meets minimum point requirements
    """
    # Calculate distances to pile center (only X, Y for cylindrical search)
    distances_2d = np.sqrt((points[:, 0] - pile_center[0])**2 + 
                          (points[:, 1] - pile_center[1])**2)
    
    # Find points within radius
    patch_mask = distances_2d <= radius
    patch_indices = np.where(patch_mask)[0]
    patch_points = points[patch_mask]
    
    # Check if patch has enough points
    is_valid = len(patch_points) >= min_points
    
    if is_valid and len(patch_points) > max_points:
        # Randomly sample to max_points
        sample_indices = np.random.choice(len(patch_points), max_points, replace=False)
        patch_points = patch_points[sample_indices]
        patch_indices = patch_indices[sample_indices]
    
    # Convert to relative coordinates (centered on pile XY, keep original Z)
    if is_valid:
        # Use drone point cloud's median Z as reference instead of IFC Z
        pile_center_corrected = np.array([pile_center[0], pile_center[1], np.median(patch_points[:, 2])])
        patch_points_relative = patch_points - pile_center_corrected
    else:
        patch_points_relative = patch_points
    
    return patch_points_relative, patch_indices, is_valid

def generate_negative_samples(points, pile_coords, patch_radius, num_negative_samples, min_distance=5.0):
    """
    Generate negative training samples (non-pile patches) from the point cloud.
    
    Args:
        points: Point cloud array (N, 3)
        pile_coords: Array of pile coordinates to avoid
        patch_radius: Radius for patch extraction
        num_negative_samples: Number of negative samples to generate
        min_distance: Minimum distance from any pile center
    
    Returns:
        negative_centers: List of negative sample center coordinates
    """
    # Get point cloud bounds
    x_min, x_max = points[:, 0].min(), points[:, 0].max()
    y_min, y_max = points[:, 1].min(), points[:, 1].max()
    
    negative_centers = []
    attempts = 0
    max_attempts = num_negative_samples * 10  # Prevent infinite loop
    
    while len(negative_centers) < num_negative_samples and attempts < max_attempts:
        # Random location within point cloud bounds
        candidate_x = np.random.uniform(x_min + patch_radius, x_max - patch_radius)
        candidate_y = np.random.uniform(y_min + patch_radius, y_max - patch_radius)
        candidate_z = np.median(points[:, 2])  # Use median Z as reference
        
        candidate_center = np.array([candidate_x, candidate_y, candidate_z])
        
        # Check distance to all pile centers
        distances_to_piles = np.sqrt(np.sum((pile_coords[:, :2] - candidate_center[:2])**2, axis=1))
        
        if np.min(distances_to_piles) >= min_distance:
            negative_centers.append(candidate_center)
        
        attempts += 1
    
    logger.info(f"Generated {len(negative_centers)} negative samples in {attempts} attempts")
    return negative_centers

def create_training_patches_z_corrected(points, ifc_metadata, patch_radius=2.0, min_points=50, max_points=2048, negative_ratio=2.0):
    """
    Create training patches from point cloud and IFC pile metadata with Z-coordinate correction.
    Handles coordinate system mismatches between drone (relative) and IFC (absolute) data.
    
    Returns:
        training_patches: List of dictionaries containing patch data
    """
    # Extract pile coordinates from IFC metadata
    coord_cols = ['X', 'Y', 'Z']
    if not all(col in ifc_metadata.columns for col in coord_cols):
        raise ValueError(f"IFC metadata must contain columns: {coord_cols}")
    
    pile_coords = ifc_metadata[coord_cols].values
    pile_numbers = ifc_metadata['Tag'].values if 'Tag' in ifc_metadata.columns else np.arange(len(pile_coords))
    
    training_patches = []
    valid_pile_count = 0
    
    logger.info(f"Creating positive training patches from {len(pile_coords)} piles...")
    
    # Create positive samples (pile patches)
    for i, (pile_coord, pile_number) in enumerate(zip(pile_coords, pile_numbers)):
        patch_points, patch_indices, is_valid = extract_pile_patch(
            points, pile_coord, patch_radius, min_points, max_points
        )
        
        if is_valid:
            patch_data = {
                'points': patch_points,
                'label': 1,  # Positive class (pile)
                'pile_number': pile_number,
                'center': pile_coord,
                'patch_type': 'positive',
                'point_count': len(patch_points)
            }
            training_patches.append(patch_data)
            valid_pile_count += 1
        
        if (i + 1) % 100 == 0:
            logger.info(f"  Processed {i + 1}/{len(pile_coords)} piles, {valid_pile_count} valid patches")
    
    logger.info(f"Created {valid_pile_count} positive training patches")
    
    # Create negative samples
    num_negative_samples = int(valid_pile_count * negative_ratio)
    logger.info(f"Creating {num_negative_samples} negative training patches...")
    
    negative_centers = generate_negative_samples(
        points, pile_coords, patch_radius, num_negative_samples
    )
    
    for i, neg_center in enumerate(negative_centers):
        patch_points, patch_indices, is_valid = extract_pile_patch(
            points, neg_center, patch_radius, min_points, max_points
        )
        
        if is_valid:
            patch_data = {
                'points': patch_points,
                'label': 0,  # Negative class (non-pile)
                'pile_number': -1,  # No pile number for negative samples
                'center': neg_center,
                'patch_type': 'negative',
                'point_count': len(patch_points)
            }
            training_patches.append(patch_data)
    
    logger.info(f"Created {len(training_patches)} total training patches")
    logger.info(f"  - Positive patches: {valid_pile_count}")
    logger.info(f"  - Negative patches: {len(training_patches) - valid_pile_count}")
    
    return training_patches

def visualize_training_patches(training_patches, sample_size=10):
    """
    Visualize sample training patches.
    """
    # Separate positive and negative patches
    positive_patches = [p for p in training_patches if p['label'] == 1]
    negative_patches = [p for p in training_patches if p['label'] == 0]
    
    # Sample patches for visualization
    pos_sample = random.sample(positive_patches, min(sample_size//2, len(positive_patches)))
    neg_sample = random.sample(negative_patches, min(sample_size//2, len(negative_patches)))
    
    fig = plt.figure(figsize=(20, 12))
    
    # Plot positive patches
    for i, patch in enumerate(pos_sample):
        ax = fig.add_subplot(2, sample_size//2, i+1, projection='3d')
        points = patch['points']
        ax.scatter(points[:, 0], points[:, 1], points[:, 2], 
                  c='red', s=1, alpha=0.6)
        ax.set_title(f"Pile {patch['pile_number']}\n{len(points)} points")
        ax.set_xlabel('X (m)')
        ax.set_ylabel('Y (m)')
        ax.set_zlabel('Z (m)')
    
    # Plot negative patches
    for i, patch in enumerate(neg_sample):
        ax = fig.add_subplot(2, sample_size//2, sample_size//2 + i+1, projection='3d')
        points = patch['points']
        ax.scatter(points[:, 0], points[:, 1], points[:, 2], 
                  c='gray', s=1, alpha=0.6)
        ax.set_title(f"Non-pile\n{len(points)} points")
        ax.set_xlabel('X (m)')
        ax.set_ylabel('Y (m)')
        ax.set_zlabel('Z (m)')
    
    plt.tight_layout()
    plt.show()
    
    # Print statistics
    logger.info(f"Training Patch Statistics:")
    logger.info(f"  Total patches: {len(training_patches)}")
    logger.info(f"  Positive patches: {len(positive_patches)}")
    logger.info(f"  Negative patches: {len(negative_patches)}")
    
    if positive_patches:
        pos_points = [p['point_count'] for p in positive_patches]
        logger.info(f"  Positive patch points: mean={np.mean(pos_points):.1f}, std={np.std(pos_points):.1f}")
    
    if negative_patches:
        neg_points = [p['point_count'] for p in negative_patches]
        logger.info(f"  Negative patch points: mean={np.mean(neg_points):.1f}, std={np.std(neg_points):.1f}")

# Create training patches with Z-coordinate correction
if 'points' in locals() and 'ifc_metadata' in locals():
    
    logger.info(f"=== Z-COORDINATE CORRECTED LABELING ===")
    logger.info(f"Handling absolute vs relative Z-coordinate systems")
    
    logger.info(f"Creating training patches with parameters:")
    logger.info(f"  - Patch radius: {patch_radius}m")
    logger.info(f"  - Min points per patch: {min_points_per_patch}")
    logger.info(f"  - Max points per patch: {max_points_per_patch}")
    logger.info(f"  - Negative sample ratio: {negative_sample_ratio}")
    
    # Create training patches with Z-coordinate correction
    training_patches = create_training_patches_z_corrected(
        points=points,
        ifc_metadata=ifc_metadata,
        patch_radius=patch_radius,
        min_points=min_points_per_patch,
        max_points=max_points_per_patch,
        negative_ratio=negative_sample_ratio
    )
    
    # Visualize sample patches
    if len(training_patches) > 0:
        visualize_training_patches(training_patches, sample_size=8)
    else:
        logger.warning("No training patches created. Check coordinate systems and parameters.")
    
else:
    logger.error("Please ensure both point cloud and IFC metadata are loaded.")

def save_labeled_data(training_patches, output_path, site_name, ground_seg_method):
    """
    Save training patches in multiple formats for ML training.
    
    Args:
        training_patches: List of training patch dictionaries
        output_path: Output directory path
        site_name: Site name for file naming
        ground_seg_method: Ground segmentation method used
    
    Returns:
        Dictionary of saved file paths
    """
    
    # Create subdirectories
    patches_dir = output_path / 'patches'
    patches_dir.mkdir(exist_ok=True)
    
    # Separate positive and negative patches
    positive_patches = [p for p in training_patches if p['label'] == 1]
    negative_patches = [p for p in training_patches if p['label'] == 0]
    
    # 1. Save individual patch files (NPZ format)
    logger.info("Saving individual training patches...")
    
    for i, patch in enumerate(positive_patches):
        patch_file = patches_dir / f"pile_{patch['pile_number']:06d}_{i:04d}.npz"
        np.savez_compressed(
            patch_file,
            points=patch['points'],
            label=patch['label'],
            pile_number=patch['pile_number'],
            center=patch['center'],
            patch_type=patch['patch_type']
        )
    
    for i, patch in enumerate(negative_patches):
        patch_file = patches_dir / f"nonpile_{i:06d}.npz"
        np.savez_compressed(
            patch_file,
            points=patch['points'],
            label=patch['label'],
            pile_number=patch['pile_number'],
            center=patch['center'],
            patch_type=patch['patch_type']
        )
    
    logger.info(f"Saved {len(training_patches)} individual patch files")
    
    # 2. Save combined training dataset (NPZ format)
    all_points = []
    all_labels = []
    all_pile_numbers = []
    patch_indices = []  # Track which points belong to which patch
    
    current_idx = 0
    for patch in training_patches:
        points = patch['points']
        all_points.append(points)
        all_labels.extend([patch['label']] * len(points))
        all_pile_numbers.extend([patch['pile_number']] * len(points))
        patch_indices.append((current_idx, current_idx + len(points)))
        current_idx += len(points)
    
    combined_points = np.vstack(all_points)
    combined_labels = np.array(all_labels)
    combined_pile_numbers = np.array(all_pile_numbers)
    
    combined_file = output_path / f"{site_name}_{ground_seg_method}_training_dataset.npz"
    np.savez_compressed(
        combined_file,
        points=combined_points,
        labels=combined_labels,
        pile_numbers=combined_pile_numbers,
        patch_indices=patch_indices
    )
    
    logger.info(f"Saved combined training dataset: {combined_file}")
    
    # 3. Save training/validation split
    train_patches, val_patches = train_test_split(
        training_patches, test_size=0.2, random_state=42, 
        stratify=[p['label'] for p in training_patches]
    )
    
    # Save train split
    train_points = np.vstack([p['points'] for p in train_patches])
    train_labels = np.array([p['label'] for patch in train_patches for p in [patch] * len(patch['points'])])
    train_pile_numbers = np.array([p['pile_number'] for patch in train_patches for p in [patch] * len(patch['points'])])
    
    train_file = output_path / f"{site_name}_{ground_seg_method}_train.npz"
    np.savez_compressed(
        train_file,
        points=train_points,
        labels=train_labels,
        pile_numbers=train_pile_numbers
    )
    
    # Save validation split
    val_points = np.vstack([p['points'] for p in val_patches])
    val_labels = np.array([p['label'] for patch in val_patches for p in [patch] * len(patch['points'])])
    val_pile_numbers = np.array([p['pile_number'] for patch in val_patches for p in [patch] * len(patch['points'])])
    
    val_file = output_path / f"{site_name}_{ground_seg_method}_val.npz"
    np.savez_compressed(
        val_file,
        points=val_points,
        labels=val_labels,
        pile_numbers=val_pile_numbers
    )
    
    logger.info(f"Saved train/validation splits: {len(train_patches)}/{len(val_patches)} patches")
    
    # 4. Save metadata and statistics
    metadata = {
        'site_name': site_name,
        'ground_segmentation_method': ground_seg_method,
        'creation_timestamp': datetime.now().isoformat(),
        'total_patches': len(training_patches),
        'positive_patches': len(positive_patches),
        'negative_patches': len(negative_patches),
        'train_patches': len(train_patches),
        'val_patches': len(val_patches),
        'total_points': len(combined_points),
        'parameters': {
            'patch_radius': patch_radius,
            'min_points_per_patch': min_points_per_patch,
            'max_points_per_patch': max_points_per_patch,
            'negative_sample_ratio': negative_sample_ratio,
            'z_filter_min': z_filter_min,
            'z_filter_max': z_filter_max
        },
        'class_distribution': {
            'positive_ratio': len(positive_patches) / len(training_patches),
            'negative_ratio': len(negative_patches) / len(training_patches)
        }
    }
    
    metadata_file = output_path / f"{site_name}_{ground_seg_method}_metadata.json"
    with open(metadata_file, 'w') as f:
        json.dump(metadata, f, indent=2)
    
    logger.info(f"Saved metadata: {metadata_file}")
    
    return {
        'patches_dir': patches_dir,
        'combined_file': combined_file,
        'train_file': train_file,
        'val_file': val_file,
        'metadata_file': metadata_file
    }

# Save the training data
if 'training_patches' in locals():
    
    logger.info("Saving labeled training data...")
    
    saved_files = save_labeled_data(
        training_patches=training_patches,
        output_path=output_path,
        site_name=site_name,
        ground_seg_method=ground_segmentation_method
    )
    
    logger.info(f"Export complete! Files saved to: {output_path}")
    logger.info(f"Saved files:")
    for file_type, file_path in saved_files.items():
        logger.info(f"  - {file_type}: {file_path}")
        
else:
    logger.error("No training patches to export. Please run the patch creation process first.")

def assess_training_quality(training_patches, ifc_metadata):
    """
    Assess the quality of generated training patches.
    """
    
    logger.info("Training Data Quality Assessment:")
    logger.info("=" * 50)
    
    # Separate positive and negative patches
    positive_patches = [p for p in training_patches if p['label'] == 1]
    negative_patches = [p for p in training_patches if p['label'] == 0]
    
    # Basic statistics
    total_patches = len(training_patches)
    total_ifc_piles = len(ifc_metadata)
    
    logger.info(f"Patch Distribution:")
    logger.info(f"  Total patches: {total_patches:,}")
    logger.info(f"  Positive patches (piles): {len(positive_patches):,}")
    logger.info(f"  Negative patches (non-piles): {len(negative_patches):,}")
    logger.info(f"  Class balance: {len(positive_patches)/total_patches*100:.1f}% positive")
    
    # Pile coverage
    coverage_rate = len(positive_patches) / total_ifc_piles if total_ifc_piles > 0 else 0
    logger.info(f"Pile Coverage:")
    logger.info(f"  Total piles in IFC: {total_ifc_piles:,}")
    logger.info(f"  Successfully extracted: {len(positive_patches):,}")
    logger.info(f"  Coverage rate: {coverage_rate*100:.1f}%")
    
    # Points per patch statistics
    if positive_patches:
        pos_points = [p['point_count'] for p in positive_patches]
        logger.info(f"Positive Patch Statistics:")
        logger.info(f"  Mean points: {np.mean(pos_points):.1f}")
        logger.info(f"  Median points: {np.median(pos_points):.1f}")
        logger.info(f"  Min points: {np.min(pos_points)}")
        logger.info(f"  Max points: {np.max(pos_points)}")
        logger.info(f"  Std points: {np.std(pos_points):.1f}")
    
    if negative_patches:
        neg_points = [p['point_count'] for p in negative_patches]
        logger.info(f"Negative Patch Statistics:")
        logger.info(f"  Mean points: {np.mean(neg_points):.1f}")
        logger.info(f"  Median points: {np.median(neg_points):.1f}")
        logger.info(f"  Min points: {np.min(neg_points)}")
        logger.info(f"  Max points: {np.max(neg_points)}")
        logger.info(f"  Std points: {np.std(neg_points):.1f}")
    
    # Quality indicators
    logger.info(f"Quality Indicators:")
    logger.info("=" * 50)
    
    if coverage_rate >= 0.8:
        logger.info(f"  Excellent pile coverage ({coverage_rate*100:.1f}%)")
    elif coverage_rate >= 0.6:
        logger.info(f"  Good pile coverage ({coverage_rate*100:.1f}%)")
    else:
        logger.info(f"  Low pile coverage ({coverage_rate*100:.1f}%) - consider adjusting parameters")
    
    class_balance = len(positive_patches) / total_patches
    if 0.2 <= class_balance <= 0.8:
        logger.info(f"  Good class balance ({class_balance*100:.1f}% positive)")
    else:
        logger.info(f"  Class imbalance detected ({class_balance*100:.1f}% positive)")
    
    if positive_patches and np.mean(pos_points) >= min_points_per_patch:
        logger.info(f"  Sufficient points per patch (avg: {np.mean(pos_points):.1f})")
    else:
        logger.info(f"  Low points per patch - consider increasing patch radius")
    
    return {
        'coverage_rate': coverage_rate,
        'class_balance': class_balance,
        'avg_points_per_positive_patch': np.mean(pos_points) if positive_patches else 0,
        'avg_points_per_negative_patch': np.mean(neg_points) if negative_patches else 0
    }

# Assess training data quality
if 'training_patches' in locals():
    quality_metrics = assess_training_quality(training_patches, ifc_metadata)
else:
    logger.error("No training patches to assess. Please run the patch creation process first.")

# Example: Load training data for ML
def load_training_patches(npz_path):
    """
    Load training patches for machine learning.
    
    Args:
        npz_path: Path to the training dataset NPZ file
    
    Returns:
        Dictionary containing training data
    """
    data = np.load(npz_path)
    return {
        'points': data['points'],
        'labels': data['labels'],
        'pile_numbers': data['pile_numbers'],
        'patch_indices': data.get('patch_indices', None)
    }

def load_individual_patches(patches_dir):
    """
    Load individual training patches for PointNet++/DGCNN training.
    
    Args:
        patches_dir: Directory containing individual patch files
    
    Returns:
        List of patch dictionaries
    """
    patch_files = list(patches_dir.glob('*.npz'))
    patches = []
    
    for patch_file in patch_files:
        data = np.load(patch_file)
        patch = {
            'points': data['points'],
            'label': int(data['label']),
            'pile_number': int(data['pile_number']),
            'center': data['center'],
            'patch_type': str(data['patch_type'])
        }
        patches.append(patch)
    
    return patches

# Example usage
logger.info("Example: Using training patches for ML training")

example_code = f"""
# Load the training dataset
train_data = load_training_patches('{output_path}/{site_name}_{ground_segmentation_method}_train.npz')
val_data = load_training_patches('{output_path}/{site_name}_{ground_segmentation_method}_val.npz')

# For PointNet++/DGCNN: Load individual patches
patches = load_individual_patches('{output_path}/patches')
positive_patches = [p for p in patches if p['label'] == 1]
negative_patches = [p for p in patches if p['label'] == 0]

# Example: Prepare data for PointNet++ training
def prepare_pointnet_data(patches, num_points=1024):
    X = []
    y = []
    
    for patch in patches:
        points = patch['points']
        
        # Resample to fixed number of points
        if len(points) >= num_points:
            indices = np.random.choice(len(points), num_points, replace=False)
            points = points[indices]
        else:
            # Pad with repeated points if necessary
            indices = np.random.choice(len(points), num_points, replace=True)
            points = points[indices]
        
        X.append(points)
        y.append(patch['label'])
    
    return np.array(X), np.array(y)

# Prepare training data
X_train, y_train = prepare_pointnet_data(patches[:int(0.8*len(patches))])
X_val, y_val = prepare_pointnet_data(patches[int(0.8*len(patches)):])

print(f"Training data shape: {{X_train.shape}}, {{y_train.shape}}")
print(f"Validation data shape: {{X_val.shape}}, {{y_val.shape}}")

# Now you can use this data with PointNet++, DGCNN, or other point cloud networks
"""

logger.info(example_code)

logger.info("\n" + "="*60)
logger.info("Next Steps:")
logger.info("1. Use training patches with PointNet++/DGCNN for pile detection")
logger.info("2. Experiment with different patch sizes and point sampling strategies")
logger.info("3. Apply trained models to new sites for automated pile detection")
logger.info("4. Validate results against ground truth data (Trino_piles.csv)")
logger.info("5. Iterate on parameters based on model performance")

# Z-Coordinate Corrected Direct Labeling
# Handle Z-coordinate differences between drone (relative) and IFC (absolute)

import numpy as np
import pandas as pd
import open3d as o3d
import matplotlib.pyplot as plt
from pathlib import Path
import json
from scipy.spatial import cKDTree

def detect_z_coordinate_mismatch(drone_points, pile_coords):
    """Detect and handle Z-coordinate system differences"""
    
    drone_z_range = drone_points[:, 2].max() - drone_points[:, 2].min()
    ifc_z_range = pile_coords[:, 2].max() - pile_coords[:, 2].min()
    
    drone_z_mean = np.mean(drone_points[:, 2])
    ifc_z_mean = np.mean(pile_coords[:, 2])
    
    z_separation = abs(drone_z_mean - ifc_z_mean)
    
    print(f"Z-Coordinate Analysis:")
    print(f"  Drone Z: {drone_points[:, 2].min():.1f} - {drone_points[:, 2].max():.1f} (range: {drone_z_range:.1f}m)")
    print(f"  IFC Z:   {pile_coords[:, 2].min():.1f} - {pile_coords[:, 2].max():.1f} (range: {ifc_z_range:.1f}m)")
    print(f"  Mean separation: {z_separation:.1f}m")
    
    # Determine coordinate system type
    if z_separation > 100:
        print(f"  🔍 DETECTED: Major Z-coordinate mismatch (likely absolute vs relative)")
        return True, z_separation
    elif z_separation > 10:
        print(f"  ⚠️ DETECTED: Moderate Z-coordinate difference")
        return True, z_separation
    else:
        print(f"  ✅ Z-coordinates appear compatible")
        return False, z_separation

def create_xy_only_labeling(drone_points, pile_coords, search_radius=4.0, min_points=15):
    """Label using XY coordinates only, ignoring Z differences"""
    
    print(f"XY-only labeling with {search_radius}m search radius...")
    print("Ignoring Z-coordinates due to coordinate system mismatch")
    
    # Use only XY coordinates for spatial search
    drone_xy = drone_points[:, :2]
    pile_xy = pile_coords[:, :2]
    
    # Build KD-tree for drone XY points
    tree = cKDTree(drone_xy)
    
    # Initialize labels
    labels = np.zeros(len(drone_points), dtype=int)
    pile_assignments = np.full(len(drone_points), -1, dtype=int)
    
    labeled_piles = 0
    total_pile_points = 0
    pile_details = []
    
    print(f"Searching for points around {len(pile_coords)} pile XY locations...")
    
    for i, pile_coord in enumerate(pile_coords):
        pile_xy_coord = pile_coord[:2]  # Use only X,Y
        
        # Find drone points within XY search radius
        indices = tree.query_ball_point(pile_xy_coord, search_radius)
        
        if len(indices) >= min_points:
            # Label these points as pile
            labels[indices] = 1
            pile_assignments[indices] = i
            
            labeled_piles += 1
            total_pile_points += len(indices)
            
            # Calculate XY distances for quality assessment
            xy_distances = np.linalg.norm(drone_xy[indices] - pile_xy_coord, axis=1)
            mean_xy_distance = np.mean(xy_distances)
            
            pile_details.append({
                'pile_id': i,
                'xy_coordinates': pile_xy_coord.tolist(),
                'point_count': len(indices),
                'mean_xy_distance': mean_xy_distance,
                'search_radius': search_radius
            })
            
            if labeled_piles <= 5:  # Show details for first few piles
                print(f"  Pile {i}: {len(indices)} points, avg XY distance {mean_xy_distance:.1f}m")
        
        if (i + 1) % 1000 == 0:  # Progress updates
            print(f"  Processed {i + 1}/{len(pile_coords)} piles")
    
    detection_rate = labeled_piles / len(pile_coords) * 100
    
    print(f"XY-only labeling complete:")
    print(f"  Labeled piles: {labeled_piles}/{len(pile_coords)} ({detection_rate:.1f}%)")
    print(f"  Pile points: {total_pile_points:,}")
    print(f"  Background points: {len(drone_points) - total_pile_points:,}")
    print(f"  Pile coverage: {total_pile_points/len(drone_points)*100:.1f}% of total points")
    
    return labels, pile_assignments, {
        'total_piles': len(pile_coords),
        'labeled_piles': labeled_piles,
        'total_pile_points': total_pile_points,
        'detection_rate': detection_rate,
        'pile_details': pile_details,
        'search_radius_used': search_radius,
        'method': 'xy_only_labeling'
    }

def adaptive_search_radius_labeling(drone_points, pile_coords, initial_radius=4.0, min_points=15):
    """Try different search radii to find optimal detection"""
    
    print("Testing different search radii for optimal detection...")
    
    # Test multiple radii
    test_radii = [2.0, 3.0, 4.0, 5.0, 6.0, 8.0, 10.0]
    results = {}
    
    drone_xy = drone_points[:, :2]
    pile_xy = pile_coords[:, :2]
    tree = cKDTree(drone_xy)
    
    for radius in test_radii:
        labeled_piles = 0
        total_points = 0
        
        for pile_coord in pile_coords:
            pile_xy_coord = pile_coord[:2]
            indices = tree.query_ball_point(pile_xy_coord, radius)
            
            if len(indices) >= min_points:
                labeled_piles += 1
                total_points += len(indices)
        
        detection_rate = labeled_piles / len(pile_coords) * 100
        coverage = total_points / len(drone_points) * 100
        
        results[radius] = {
            'detection_rate': detection_rate,
            'labeled_piles': labeled_piles,
            'total_points': total_points,
            'coverage': coverage
        }
        
        print(f"  Radius {radius}m: {labeled_piles}/{len(pile_coords)} piles ({detection_rate:.1f}%), {coverage:.1f}% coverage")
    
    # Find optimal radius (balance between detection rate and reasonable coverage)
    best_radius = 4.0
    best_score = 0
    
    for radius, result in results.items():
        # Score function: prioritize detection rate but penalize excessive coverage
        if result['coverage'] < 50:  # Reasonable upper bound
            score = result['detection_rate'] - (result['coverage'] / 10)  # Slight penalty for high coverage
            if score > best_score:
                best_score = score
                best_radius = radius
    
    print(f"Optimal radius selected: {best_radius}m")
    
    # Run full labeling with optimal radius
    return create_xy_only_labeling(drone_points, pile_coords, best_radius, min_points)

def visualize_xy_results(drone_points, labels, pile_coords, sample_ratio=0.1):
    """Enhanced visualization focusing on XY plane"""
    
    # Sample for visualization
    n_sample = int(len(drone_points) * sample_ratio)
    sample_indices = np.random.choice(len(drone_points), n_sample, replace=False)
    sample_points = drone_points[sample_indices]
    sample_labels = labels[sample_indices]
    
    fig = plt.figure(figsize=(20, 10))
    
    # XY Top view (main view)
    ax1 = fig.add_subplot(221)
    
    bg_mask = sample_labels == 0
    pile_mask = sample_labels == 1
    
    if np.any(bg_mask):
        ax1.scatter(sample_points[bg_mask, 0], sample_points[bg_mask, 1], 
                   c='lightgray', s=2, alpha=0.4, label='Background')
    
    if np.any(pile_mask):
        ax1.scatter(sample_points[pile_mask, 0], sample_points[pile_mask, 1], 
                   c='red', s=10, alpha=0.8, label='Detected Piles')
    
    # Add IFC pile centers
    ax1.scatter(pile_coords[:, 0], pile_coords[:, 1], 
               c='blue', s=5, marker='x', alpha=0.6, label='IFC Pile Centers')
    
    ax1.set_xlabel('X (Easting)')
    ax1.set_ylabel('Y (Northing)')
    ax1.set_title('XY-Only Labeling Results (Top View)')
    ax1.legend()
    ax1.axis('equal')
    ax1.grid(True, alpha=0.3)
    
    # Zoomed view of a small region
    ax2 = fig.add_subplot(222)
    
    # Focus on a 200m x 200m region with piles
    if len(pile_coords) > 0:
        center_x = pile_coords[len(pile_coords)//2, 0]
        center_y = pile_coords[len(pile_coords)//2, 1]
        
        # Filter to zoom region
        zoom_size = 100  # 100m radius
        zoom_mask = ((sample_points[:, 0] >= center_x - zoom_size) & 
                     (sample_points[:, 0] <= center_x + zoom_size) &
                     (sample_points[:, 1] >= center_y - zoom_size) & 
                     (sample_points[:, 1] <= center_y + zoom_size))
        
        if np.any(zoom_mask):
            zoom_points = sample_points[zoom_mask]
            zoom_labels = sample_labels[zoom_mask]
            
            zoom_bg = zoom_labels == 0
            zoom_pile = zoom_labels == 1
            
            if np.any(zoom_bg):
                ax2.scatter(zoom_points[zoom_bg, 0], zoom_points[zoom_bg, 1], 
                           c='lightgray', s=5, alpha=0.4, label='Background')
            
            if np.any(zoom_pile):
                ax2.scatter(zoom_points[zoom_pile, 0], zoom_points[zoom_pile, 1], 
                           c='red', s=20, alpha=0.8, label='Detected Piles')
        
        # Add nearby IFC piles
        pile_zoom_mask = ((pile_coords[:, 0] >= center_x - zoom_size) & 
                          (pile_coords[:, 0] <= center_x + zoom_size) &
                          (pile_coords[:, 1] >= center_y - zoom_size) & 
                          (pile_coords[:, 1] <= center_y + zoom_size))
        
        if np.any(pile_zoom_mask):
            zoom_piles = pile_coords[pile_zoom_mask]
            ax2.scatter(zoom_piles[:, 0], zoom_piles[:, 1], 
                       c='blue', s=30, marker='x', alpha=0.8, label='IFC Piles')
    
    ax2.set_xlabel('X (Easting)')
    ax2.set_ylabel('Y (Northing)')
    ax2.set_title('Zoomed View (200m x 200m)')
    ax2.legend()
    ax2.axis('equal')
    ax2.grid(True, alpha=0.3)
    
    # Label distribution
    ax3 = fig.add_subplot(223)
    label_counts = np.bincount(labels)
    ax3.bar(['Background', 'Pile'], label_counts, color=['lightgray', 'red'], alpha=0.7)
    ax3.set_ylabel('Number of Points')
    ax3.set_title('Label Distribution')
    
    for i, count in enumerate(label_counts):
        ax3.text(i, count + max(label_counts)*0.01, f'{count:,}', ha='center')
    
    # Detection statistics
    ax4 = fig.add_subplot(224)
    ax4.axis('off')
    
    detection_rate = np.sum(labels == 1) / len(labels) * 100 if len(labels) > 0 else 0
    
    stats_text = f"""
XY-Only Labeling Results:

Total Points: {len(drone_points):,}
Total IFC Piles: {len(pile_coords):,}
Pile Points Found: {np.sum(labels == 1):,}
Background Points: {np.sum(labels == 0):,}

Pile Coverage: {np.sum(labels == 1)/len(drone_points)*100:.1f}%
Method: XY coordinates only
Z-difference ignored: ~150m

Key Insight:
Drone Z: 1-13m (relative heights)
IFC Z: 155-159m (absolute elevation)
Solution: Ignore Z, use XY only
    """
    
    ax4.text(0.05, 0.95, stats_text, transform=ax4.transAxes, 
            fontsize=10, verticalalignment='top', fontfamily='monospace')
    
    plt.tight_layout()
    plt.show()

def run_z_corrected_labeling(drone_file, ifc_file, search_radius=None, output_dir="../data/labeled_data"):
    """Complete Z-coordinate corrected labeling pipeline"""
    
    print("=== Z-COORDINATE CORRECTED LABELING ===")
    print("Handling absolute vs relative Z-coordinate systems")
    
    # Load data with LAS support
    if str(drone_file).endswith('.npy'):
        drone_points = np.load(drone_file)
    elif str(drone_file).endswith('.ply'):
        pcd = o3d.io.read_point_cloud(str(drone_file))
        drone_points = np.asarray(pcd.points)
    elif str(drone_file).endswith('.las'):
        try:
            import laspy
            las_file = laspy.read(drone_file)
            drone_points = np.vstack([las_file.x, las_file.y, las_file.z]).transpose()
            print(f"Loaded LAS file with {len(drone_points):,} points")
        except ImportError:
            print("Error: laspy not installed. Install with: pip install laspy")
            print("Alternative: Convert LAS to PLY first")
            return None
    else:
        raise ValueError("Supported formats: .npy, .ply, .las")
    
    ifc_df = pd.read_csv(ifc_file)
    pile_coords = ifc_df[['X', 'Y', 'Z']].values
    
    print(f"Loaded {len(drone_points):,} drone points")
    print(f"Loaded {len(pile_coords):,} pile coordinates")
    
    # Detect Z-coordinate mismatch
    has_z_mismatch, z_separation = detect_z_coordinate_mismatch(drone_points, pile_coords)
    
    if has_z_mismatch:
        print("Using XY-only labeling to handle Z-coordinate mismatch")
        
        if search_radius is None:
            # Use adaptive search to find optimal parameters
            labels, pile_assignments, pile_info = adaptive_search_radius_labeling(
                drone_points, pile_coords, initial_radius=4.0, min_points=15
            )
        else:
            # Use specified search radius
            print(f"Using specified search radius: {search_radius}m")
            labels, pile_assignments, pile_info = create_xy_only_labeling(
                drone_points, pile_coords, search_radius=search_radius, min_points=15
            )
    else:
        print("Using full XYZ labeling")
        # Use original 3D labeling
        from scipy.spatial import cKDTree
        tree = cKDTree(drone_points)
        # ... implement 3D labeling if needed
    
    # Visualize results
    visualize_xy_results(drone_points, labels, pile_coords)
    
    # Save results
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    # Save labeled data
    labeled_data = {
        'points': drone_points,
        'labels': labels,
        'pile_assignments': pile_assignments
    }
    np.savez_compressed(output_path / "xy_labeled_pointcloud.npz", **labeled_data)
    
    # Save pile info
    with open(output_path / "xy_labeling_info.json", 'w') as f:
        json.dump(pile_info, f, indent=2)
    
    # Create training patches if sufficient pile points found
    if pile_info['total_pile_points'] > 1000:
        pile_mask = labels == 1
        background_mask = labels == 0
        
        pile_points = drone_points[pile_mask]
        background_points = drone_points[background_mask]
        
        # Create balanced patches
        patch_size = 1024
        n_pile_patches = len(pile_points) // patch_size
        
        if n_pile_patches > 0:
            patches = []
            patch_labels = []
            
            # Pile patches
            for i in range(n_pile_patches):
                start_idx = i * patch_size
                end_idx = start_idx + patch_size
                patch = pile_points[start_idx:end_idx]
                patches.append(patch)
                patch_labels.append(1)
            
            # Background patches
            for i in range(n_pile_patches):
                indices = np.random.choice(len(background_points), patch_size, replace=True)
                patch = background_points[indices]
                patches.append(patch)
                patch_labels.append(0)
            
            # Save patches
            np.save(output_path / "training_patches.npy", np.array(patches))
            np.save(output_path / "patch_labels.npy", np.array(patch_labels))
            
            print(f"Created {len(patches)} training patches")
    
    print(f"\nZ-corrected labeling complete!")
    print(f"Detection rate: {pile_info['detection_rate']:.1f}%")
    print(f"Total pile points: {pile_info['total_pile_points']:,}")
    print(f"Results saved to: {output_path}")
    
    return {
        'drone_points': drone_points,
        'labels': labels,
        'pile_info': pile_info,
        'output_path': output_path
    }

# Run the corrected labeling with different options
if __name__ == "__main__":
    
    # Option 1: LAS file with adaptive radius
    # results1 = run_z_corrected_labeling(
    #     drone_file="trino_enel_original.las",  # LAS file
    #     ifc_file="pile_metadata.csv"
    #     # search_radius=None uses adaptive radius selection
    # )
    
    # Option 2: LAS file with large fixed radius
    results2 = run_z_corrected_labeling(
        #drone_file = "../../../data/processed/trino_enel/ground_segmentation/csf/trino_enel_nonground.ply",
        drone_file = "../../../data/raw/trino_enel/pointcloud/Trino_Fly_2_Shifted.las",
        ifc_file = "../../../data/processed/trino_enel/ifc_metadata/GRE.EEC.S.00.IT.P.14353.00.265_coordinates.csv",
        search_radius=10.0  # Large radius for better detection
    )
    
    # Option 3: Try different point cloud if available
    # results3 = run_z_corrected_labeling(
    #     drone_file="trino_enel_ground_filtered.ply",
    #     ifc_file="pile_metadata.csv",
    #     search_radius=8.0
    # )

!ls -lh ../../../data/raw/trino_enel/pointcloud/