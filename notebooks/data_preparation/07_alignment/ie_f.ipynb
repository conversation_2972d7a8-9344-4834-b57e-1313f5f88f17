# Feature-Based ICP Improvement Notebook
# Improving earlier ICP results using geometric feature alignment

import numpy as np
import open3d as o3d
import matplotlib.pyplot as plt
from pathlib import Path
from scipy.spatial import cKDTree
from scipy.spatial.distance import cdist
from sklearn.cluster import DBSCAN
from sklearn.neighbors import NearestNeighbors
import json
import time
from datetime import datetime

# Configuration
ALIGNED_DRONE_FILE = "../../../data/processed/icp_alignment_minimal/aligned_drone_points.npy"
IFC_FILE = "../../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply"
OUTPUT_DIR = "../../../data/processed/feature_aligned"

class GeometricFeatureExtractor:
    """Extract geometric features from point clouds"""
    
    def __init__(self, voxel_size=0.5, min_points=50):
        self.voxel_size = voxel_size
        self.min_points = min_points
        
    def extract_corner_features(self, points):
        """Extract corner/edge features from point cloud"""
        print(f"Extracting corner features from {len(points):,} points...")
        
        # Create point cloud
        pcd = o3d.geometry.PointCloud()
        pcd.points = o3d.utility.Vector3dVector(points)
        
        # Downsample for efficiency
        pcd = pcd.voxel_down_sample(self.voxel_size)
        
        # Estimate normals
        pcd.estimate_normals(
            search_param=o3d.geometry.KDTreeSearchParamHybrid(
                radius=self.voxel_size * 2, max_nn=30
            )
        )
        
        points_ds = np.asarray(pcd.points)
        normals = np.asarray(pcd.normals)
        
        # Calculate curvature for each point
        curvatures = self._calculate_curvature(points_ds, normals)
        
        # Extract high-curvature points (corners/edges)
        curvature_threshold = np.percentile(curvatures, 90)  # Top 10% curvature
        corner_indices = curvatures > curvature_threshold
        
        corner_points = points_ds[corner_indices]
        corner_curvatures = curvatures[corner_indices]
        
        print(f"Found {len(corner_points)} corner/edge points")
        
        return {
            'points': corner_points,
            'curvatures': corner_curvatures,
            'normals': normals[corner_indices],
            'all_points': points_ds,
            'all_curvatures': curvatures
        }
    
    def extract_planar_features(self, points):
        """Extract planar surfaces (walls, floors, etc.)"""
        print(f"Extracting planar features from {len(points):,} points...")
        
        pcd = o3d.geometry.PointCloud()
        pcd.points = o3d.utility.Vector3dVector(points)
        pcd = pcd.voxel_down_sample(self.voxel_size)
        
        # RANSAC plane detection
        planes = []
        remaining_pcd = pcd
        
        for i in range(5):  # Extract up to 5 major planes
            if len(remaining_pcd.points) < self.min_points:
                break
                
            plane_model, inliers = remaining_pcd.segment_plane(
                distance_threshold=0.1,
                ransac_n=3,
                num_iterations=1000
            )
            
            if len(inliers) < self.min_points:
                break
                
            plane_pcd = remaining_pcd.select_by_index(inliers)
            plane_points = np.asarray(plane_pcd.points)
            
            planes.append({
                'points': plane_points,
                'model': plane_model,  # [a, b, c, d] for ax + by + cz + d = 0
                'centroid': np.mean(plane_points, axis=0),
                'normal': plane_model[:3]
            })
            
            # Remove plane points for next iteration
            remaining_pcd = remaining_pcd.select_by_index(inliers, invert=True)
            
        print(f"Found {len(planes)} planar surfaces")
        return planes
    
    def extract_linear_features(self, points):
        """Extract linear features (edges, beams, etc.)"""
        print(f"Extracting linear features from {len(points):,} points...")
        
        pcd = o3d.geometry.PointCloud()
        pcd.points = o3d.utility.Vector3dVector(points)
        pcd = pcd.voxel_down_sample(self.voxel_size)
        
        points_ds = np.asarray(pcd.points)
        
        # Use DBSCAN to cluster points into potential linear structures
        clustering = DBSCAN(eps=self.voxel_size * 2, min_samples=10).fit(points_ds)
        labels = clustering.labels_
        
        linear_features = []
        
        for label in set(labels):
            if label == -1:  # Noise
                continue
                
            cluster_points = points_ds[labels == label]
            
            if len(cluster_points) < 20:  # Too small for reliable line fitting
                continue
                
            # Fit line using PCA
            centroid = np.mean(cluster_points, axis=0)
            centered_points = cluster_points - centroid
            
            # SVD for PCA
            U, S, Vt = np.linalg.svd(centered_points)
            
            # Check if points form a line (first eigenvalue >> others)
            if S[0] > 3 * S[1]:  # Linear structure
                direction = Vt[0]  # Principal component
                
                linear_features.append({
                    'points': cluster_points,
                    'centroid': centroid,
                    'direction': direction,
                    'length': S[0],
                    'linearity': S[0] / (S[1] + 1e-6)
                })
        
        print(f"Found {len(linear_features)} linear features")
        return linear_features
    
    def _calculate_curvature(self, points, normals, k=20):
        """Calculate curvature at each point"""
        curvatures = np.zeros(len(points))
        
        # Build KD-tree for neighbor search
        tree = cKDTree(points)
        
        for i, point in enumerate(points):
            # Find k nearest neighbors
            distances, indices = tree.query(point, k=k+1)
            neighbor_indices = indices[1:]  # Exclude the point itself
            
            if len(neighbor_indices) < k:
                continue
                
            # Get neighbor normals
            neighbor_normals = normals[neighbor_indices]
            current_normal = normals[i]
            
            # Calculate curvature as variance in normal directions
            normal_deviations = np.abs(np.dot(neighbor_normals, current_normal))
            curvature = 1.0 - np.mean(normal_deviations)
            curvatures[i] = curvature
            
        return curvatures

class FeatureMatcher:
    """Match corresponding features between point clouds"""
    
    def __init__(self, distance_threshold=2.0):
        self.distance_threshold = distance_threshold
        
    def match_corner_features(self, drone_features, ifc_features):
        """Match corner features between drone and IFC data"""
        print("Matching corner features...")
        
        drone_corners = drone_features['points']
        ifc_corners = ifc_features['points']
        
        # Calculate pairwise distances
        distances = cdist(drone_corners, ifc_corners)
        
        # Find best matches using Hungarian algorithm approximation
        matches = []
        used_ifc = set()
        
        # Sort drone corners by curvature (highest first)
        drone_order = np.argsort(-drone_features['curvatures'])
        
        for i in drone_order:
            # Find closest unused IFC corner
            available_distances = distances[i].copy()
            for used_idx in used_ifc:
                available_distances[used_idx] = np.inf
                
            min_dist_idx = np.argmin(available_distances)
            min_dist = available_distances[min_dist_idx]
            
            if min_dist < self.distance_threshold:
                matches.append({
                    'drone_idx': i,
                    'ifc_idx': min_dist_idx,
                    'drone_point': drone_corners[i],
                    'ifc_point': ifc_corners[min_dist_idx],
                    'distance': min_dist,
                    'confidence': 1.0 / (1.0 + min_dist)
                })
                used_ifc.add(min_dist_idx)
        
        print(f"Found {len(matches)} corner matches")
        return matches
    
    def match_planar_features(self, drone_planes, ifc_planes):
        """Match planar features"""
        print("Matching planar features...")
        
        matches = []
        
        for i, drone_plane in enumerate(drone_planes):
            best_match = None
            best_score = 0
            
            for j, ifc_plane in enumerate(ifc_planes):
                # Compare normal vectors
                normal_similarity = np.abs(np.dot(drone_plane['normal'], ifc_plane['normal']))
                
                # Compare centroid distances
                centroid_distance = np.linalg.norm(drone_plane['centroid'] - ifc_plane['centroid'])
                distance_score = 1.0 / (1.0 + centroid_distance / 10.0)
                
                # Combined score
                score = normal_similarity * distance_score
                
                if score > best_score and score > 0.3:  # Minimum similarity threshold
                    best_score = score
                    best_match = {
                        'drone_idx': i,
                        'ifc_idx': j,
                        'drone_centroid': drone_plane['centroid'],
                        'ifc_centroid': ifc_plane['centroid'],
                        'normal_similarity': normal_similarity,
                        'confidence': score
                    }
            
            if best_match:
                matches.append(best_match)
        
        print(f"Found {len(matches)} planar matches")
        return matches
    
    def match_linear_features(self, drone_lines, ifc_lines):
        """Match linear features"""
        print("Matching linear features...")
        
        matches = []
        
        for i, drone_line in enumerate(drone_lines):
            best_match = None
            best_score = 0
            
            for j, ifc_line in enumerate(ifc_lines):
                # Compare direction vectors
                direction_similarity = np.abs(np.dot(drone_line['direction'], ifc_line['direction']))
                
                # Compare centroid distances
                centroid_distance = np.linalg.norm(drone_line['centroid'] - ifc_line['centroid'])
                distance_score = 1.0 / (1.0 + centroid_distance / 5.0)
                
                # Combined score
                score = direction_similarity * distance_score
                
                if score > best_score and score > 0.4:
                    best_score = score
                    best_match = {
                        'drone_idx': i,
                        'ifc_idx': j,
                        'drone_centroid': drone_line['centroid'],
                        'ifc_centroid': ifc_line['centroid'],
                        'direction_similarity': direction_similarity,
                        'confidence': score
                    }
            
            if best_match:
                matches.append(best_match)
        
        print(f"Found {len(matches)} linear matches")
        return matches

class TransformationCalculator:
    """Calculate transformation from feature correspondences"""
    
    def calculate_feature_transformation(self, feature_pairs):
        """Calculate transformation from feature correspondences"""
        print(f"Calculating transformation from {len(feature_pairs)} feature pairs...")
        
        if len(feature_pairs) < 3:
            raise ValueError("Need at least 3 feature pairs for transformation calculation")
        
        # Extract point correspondences
        source_points = []
        target_points = []
        weights = []
        
        for pair in feature_pairs:
            if 'drone_point' in pair:  # Corner features
                source_points.append(pair['drone_point'])
                target_points.append(pair['ifc_point'])
            else:  # Planar/linear features (use centroids)
                source_points.append(pair['drone_centroid'])
                target_points.append(pair['ifc_centroid'])
            
            weights.append(pair['confidence'])
        
        source_points = np.array(source_points)
        target_points = np.array(target_points)
        weights = np.array(weights)
        
        # Weighted least squares transformation
        transformation = self._solve_weighted_transformation(
            source_points, target_points, weights
        )
        
        return transformation
    
    def _solve_weighted_transformation(self, source, target, weights):
        """Solve for weighted rigid transformation"""
        # Normalize weights
        weights = weights / np.sum(weights)
        
        # Weighted centroids
        source_centroid = np.average(source, axis=0, weights=weights)
        target_centroid = np.average(target, axis=0, weights=weights)
        
        # Center the points
        source_centered = source - source_centroid
        target_centered = target - target_centroid
        
        # Weighted covariance matrix
        H = np.zeros((3, 3))
        for i in range(len(source)):
            H += weights[i] * np.outer(source_centered[i], target_centered[i])
        
        # SVD for rotation
        U, S, Vt = np.linalg.svd(H)
        R = Vt.T @ U.T
        
        # Ensure proper rotation (det(R) = 1)
        if np.linalg.det(R) < 0:
            Vt[-1, :] *= -1
            R = Vt.T @ U.T
        
        # Translation
        t = target_centroid - R @ source_centroid
        
        # Build 4x4 transformation matrix
        transformation = np.eye(4)
        transformation[:3, :3] = R
        transformation[:3, 3] = t
        
        return transformation

def feature_based_alignment(drone_points, ifc_points):
    """Main feature-based alignment function"""
    print("="*60)
    print("FEATURE-BASED ALIGNMENT")
    print("="*60)
    
    # Initialize components
    extractor = GeometricFeatureExtractor(voxel_size=0.5)
    matcher = FeatureMatcher(distance_threshold=3.0)
    calculator = TransformationCalculator()
    
    # Extract features from both datasets
    print("\n1. FEATURE EXTRACTION")
    print("-" * 30)
    
    drone_corners = extractor.extract_corner_features(drone_points)
    ifc_corners = extractor.extract_corner_features(ifc_points)
    
    drone_planes = extractor.extract_planar_features(drone_points)
    ifc_planes = extractor.extract_planar_features(ifc_points)
    
    drone_lines = extractor.extract_linear_features(drone_points)
    ifc_lines = extractor.extract_linear_features(ifc_points)
    
    # Match corresponding features
    print("\n2. FEATURE MATCHING")
    print("-" * 30)
    
    corner_matches = matcher.match_corner_features(drone_corners, ifc_corners)
    planar_matches = matcher.match_planar_features(drone_planes, ifc_planes)
    linear_matches = matcher.match_linear_features(drone_lines, ifc_lines)
    
    # Combine all matches
    all_matches = corner_matches + planar_matches + linear_matches
    
    if len(all_matches) < 3:
        raise ValueError(f"Insufficient feature matches found: {len(all_matches)}")
    
    print(f"Total feature matches: {len(all_matches)}")
    
    # Calculate transformation
    print("\n3. TRANSFORMATION CALCULATION")
    print("-" * 30)
    
    transformation = calculator.calculate_feature_transformation(all_matches)
    
    # Quality assessment
    alignment_error = assess_feature_alignment(all_matches, transformation)
    
    print(f"Feature alignment RMSE: {alignment_error:.4f}m")
    
    return {
        'transformation': transformation,
        'corner_matches': corner_matches,
        'planar_matches': planar_matches,
        'linear_matches': linear_matches,
        'all_matches': all_matches,
        'alignment_error': alignment_error,
        'drone_features': {
            'corners': drone_corners,
            'planes': drone_planes,
            'lines': drone_lines
        },
        'ifc_features': {
            'corners': ifc_corners,
            'planes': ifc_planes,
            'lines': ifc_lines
        }
    }

def assess_feature_alignment(matches, transformation):
    """Assess quality of feature alignment"""
    errors = []
    
    for match in matches:
        if 'drone_point' in match:
            source_pt = match['drone_point']
            target_pt = match['ifc_point']
        else:
            source_pt = match['drone_centroid']
            target_pt = match['ifc_centroid']
        
        # Transform source point
        source_h = np.append(source_pt, 1.0)
        transformed_pt = (transformation @ source_h)[:3]
        
        # Calculate error
        error = np.linalg.norm(transformed_pt - target_pt)
        errors.append(error)
    
    return np.sqrt(np.mean(np.array(errors)**2))

def refine_with_icp(source_points, target_points, initial_transformation):
    """Refine feature-based alignment with ICP"""
    print("\n4. ICP REFINEMENT")
    print("-" * 30)
    
    # Apply initial transformation
    source_h = np.ones((len(source_points), 4))
    source_h[:, :3] = source_points
    source_transformed = (source_h @ initial_transformation.T)[:, :3]
    
    # Create point clouds
    source_pcd = o3d.geometry.PointCloud()
    source_pcd.points = o3d.utility.Vector3dVector(source_transformed)
    
    target_pcd = o3d.geometry.PointCloud()
    target_pcd.points = o3d.utility.Vector3dVector(target_points)
    
    # Downsample for ICP
    source_pcd = source_pcd.voxel_down_sample(0.2)
    target_pcd = target_pcd.voxel_down_sample(0.4)
    
    # Estimate normals
    source_pcd.estimate_normals()
    target_pcd.estimate_normals()
    
    print(f"ICP input: {len(source_pcd.points):,} source, {len(target_pcd.points):,} target")
    
    # Progressive ICP
    distances = [1.0, 0.5, 0.2]
    iterations = [50, 100, 200]
    
    current_transform = np.eye(4)
    
    for i, (dist, iter_count) in enumerate(zip(distances, iterations)):
        print(f"  ICP stage {i+1}: distance={dist}, iterations={iter_count}")
        
        try:
            result = o3d.pipelines.registration.registration_icp(
                source_pcd, target_pcd,
                max_correspondence_distance=dist,
                init=current_transform,
                estimation_method=o3d.pipelines.registration.TransformationEstimationPointToPlane(),
                criteria=o3d.pipelines.registration.ICPConvergenceCriteria(
                    max_iteration=iter_count,
                    relative_fitness=1e-6,
                    relative_rmse=1e-6
                )
            )
            
            print(f"    Fitness: {result.fitness:.6f}, RMSE: {result.inlier_rmse:.6f}")
            
            # Check for reasonable convergence
            translation_mag = np.linalg.norm(result.transformation[:3, 3])
            if translation_mag > 5.0:  # More lenient than before
                print(f"    Large translation ({translation_mag:.2f}m), stopping")
                break
                
            current_transform = result.transformation
            
        except Exception as e:
            print(f"    ICP failed: {e}")
            break
    
    # Combine transformations
    final_transformation = current_transform @ initial_transformation
    
    return final_transformation, result if 'result' in locals() else None

def evaluate_improved_alignment(original_points, aligned_points, ifc_points):
    """Evaluate the improved alignment"""
    print("\n5. ALIGNMENT EVALUATION")
    print("-" * 30)
    
    # Sample for evaluation
    sample_size = 10000
    aligned_sample = aligned_points[np.random.choice(len(aligned_points), 
                                                   min(sample_size, len(aligned_points)), 
                                                   replace=False)]
    ifc_sample = ifc_points[np.random.choice(len(ifc_points), 
                                           min(sample_size, len(ifc_points)), 
                                           replace=False)]
    
    # Calculate distances
    tree = cKDTree(ifc_sample)
    distances, _ = tree.query(aligned_sample)
    
    # Metrics
    metrics = {
        'rmse': np.sqrt(np.mean(distances**2)),
        'median': np.median(distances),
        'mean': np.mean(distances),
        'std': np.std(distances),
        'pct_1cm': np.sum(distances < 0.01) / len(distances) * 100,
        'pct_5cm': np.sum(distances < 0.05) / len(distances) * 100,
        'pct_10cm': np.sum(distances < 0.10) / len(distances) * 100,
        'pct_50cm': np.sum(distances < 0.50) / len(distances) * 100,
        'pct_1m': np.sum(distances < 1.0) / len(distances) * 100
    }
    
    print(f"RMSE: {metrics['rmse']:.4f}m")
    print(f"Median: {metrics['median']:.4f}m")
    print(f"Centimeter accuracy:")
    print(f"  < 1cm: {metrics['pct_1cm']:.1f}%")
    print(f"  < 5cm: {metrics['pct_5cm']:.1f}%")
    print(f"  < 10cm: {metrics['pct_10cm']:.1f}%")
    print(f"Sub-meter accuracy:")
    print(f"  < 50cm: {metrics['pct_50cm']:.1f}%")
    print(f"  < 1m: {metrics['pct_1m']:.1f}%")
    
    return metrics

def visualize_features(drone_features, ifc_features, matches, save_path=None):
    """Visualize extracted features and matches"""
    fig = plt.figure(figsize=(15, 10))
    
    # Plot 1: Corner features
    ax1 = fig.add_subplot(221, projection='3d')
    
    drone_corners = drone_features['corners']['points']
    ifc_corners = ifc_features['corners']['points']
    
    ax1.scatter(drone_corners[:, 0], drone_corners[:, 1], drone_corners[:, 2], 
               c='red', s=20, alpha=0.6, label='Drone corners')
    ax1.scatter(ifc_corners[:, 0], ifc_corners[:, 1], ifc_corners[:, 2], 
               c='blue', s=20, alpha=0.6, label='IFC corners')
    
    # Draw matches
    corner_matches = [m for m in matches if 'drone_point' in m]
    for match in corner_matches[:10]:  # Show first 10 matches
        ax1.plot([match['drone_point'][0], match['ifc_point'][0]],
                [match['drone_point'][1], match['ifc_point'][1]],
                [match['drone_point'][2], match['ifc_point'][2]], 
                'g-', alpha=0.5)
    
    ax1.set_title('Corner Feature Matches')
    ax1.legend()
    
    # Plot 2: Feature distribution
    ax2 = fig.add_subplot(222)
    
    all_drone_curv = drone_features['corners']['all_curvatures']
    ax2.hist(all_drone_curv, bins=50, alpha=0.7, label='Drone curvature', density=True)
    ax2.axvline(np.percentile(all_drone_curv, 90), color='red', linestyle='--', 
               label='Corner threshold (90th percentile)')
    ax2.set_xlabel('Curvature')
    ax2.set_ylabel('Density')
    ax2.set_title('Curvature Distribution')
    ax2.legend()
    
    # Plot 3: Match confidence
    ax3 = fig.add_subplot(223)
    
    confidences = [m['confidence'] for m in matches]
    match_types = ['Corner' if 'drone_point' in m else 'Planar/Linear' for m in matches]
    
    colors = ['red' if t == 'Corner' else 'blue' for t in match_types]
    ax3.scatter(range(len(confidences)), confidences, c=colors, alpha=0.7)
    ax3.set_xlabel('Match Index')
    ax3.set_ylabel('Confidence')
    ax3.set_title('Feature Match Confidence')
    ax3.grid(True, alpha=0.3)
    
    # Plot 4: Error distribution
    ax4 = fig.add_subplot(224)
    
    if 'distances' in locals():
        ax4.hist(distances, bins=50, alpha=0.7, edgecolor='black')
        ax4.axvline(np.median(distances), color='red', linestyle='--', 
                   label=f'Median: {np.median(distances):.3f}m')
        ax4.set_xlabel('Distance to nearest IFC point (m)')
        ax4.set_ylabel('Frequency')
        ax4.set_title('Final Alignment Error Distribution')
        ax4.legend()
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Visualization saved to {save_path}")
    
    plt.show()

def save_results(aligned_points, transformation, metrics, feature_results, output_dir):
    """Save all results"""
    print(f"\n6. SAVING RESULTS")
    print("-" * 30)
    
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    # Save aligned points
    np.save(output_path / "feature_aligned_points.npy", aligned_points)
    
    # Save transformation
    np.save(output_path / "feature_transformation.npy", transformation)
    
    # Save point cloud
    aligned_pcd = o3d.geometry.PointCloud()
    aligned_pcd.points = o3d.utility.Vector3dVector(aligned_points)
    o3d.io.write_point_cloud(str(output_path / "feature_aligned_drone.ply"), aligned_pcd)
    
    # Save detailed report
    report = {
        'timestamp': datetime.now().isoformat(),
        'method': 'feature_based_alignment',
        'metrics': {k: float(v) for k, v in metrics.items()},
        'transformation_matrix': transformation.tolist(),
        'feature_statistics': {
            'corner_matches': len(feature_results['corner_matches']),
            'planar_matches': len(feature_results['planar_matches']),
            'linear_matches': len(feature_results['linear_matches']),
            'total_matches': len(feature_results['all_matches']),
            'feature_alignment_rmse': float(feature_results['alignment_error'])
        },
        'feature_details': {
            'drone_corners': len(feature_results['drone_features']['corners']['points']),
            'ifc_corners': len(feature_results['ifc_features']['corners']['points']),
            'drone_planes': len(feature_results['drone_features']['planes']),
            'ifc_planes': len(feature_results['ifc_features']['planes']),
            'drone_lines': len(feature_results['drone_features']['lines']),
            'ifc_lines': len(feature_results['ifc_features']['lines'])
        }
    }
    
    with open(output_path / "feature_alignment_report.json", 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"Results saved to {output_path}")
    return report

def main():
    """Main feature-based improvement pipeline"""
    print("Feature-Based ICP Improvement Pipeline")
    print("=" * 60)
    
    # Load data
    print("\nLoading data...")
    drone_points = np.load(ALIGNED_DRONE_FILE)
    
    ifc_pcd = o3d.io.read_point_cloud(str(IFC_FILE))
    ifc_points = np.asarray(ifc_pcd.points)
    
    print(f"Loaded drone points: {len(drone_points):,}")
    print(f"Loaded IFC points: {len(ifc_points):,}")
    
    try:
        # Feature-based alignment
        feature_results = feature_based_alignment(drone_points, ifc_points)
        
        # Refine with ICP
        final_transformation, icp_result = refine_with_icp(
            drone_points, ifc_points, feature_results['transformation']
        )
        
        # Apply final transformation
        drone_h = np.ones((len(drone_points), 4))
        drone_h[:, :3] = drone_points
        final_aligned_points = (drone_h @ final_transformation.T)[:, :3]
        
        # Evaluate results
        metrics = evaluate_improved_alignment(drone_points, final_aligned_points, ifc_points)
        
        # Visualize results
        visualize_features(
            feature_results['drone_features'], 
            feature_results['ifc_features'],
            feature_results['all_matches'],
            save_path=Path(OUTPUT_DIR) / "feature_visualization.png"
        )
        
        # Save results
        report = save_results(
            final_aligned_points, 
            final_transformation, 
            metrics, 
            feature_results, 
            OUTPUT_DIR
        )
        
        # Print summary
        print("\n" + "="*60)
        print("FEATURE-BASED IMPROVEMENT COMPLETE")
        print("="*60)
        
        print(f"\nFeature Extraction:")
        print(f"  Corner features: {len(feature_results['corner_matches'])} matches")
        print(f"  Planar features: {len(feature_results['planar_matches'])} matches")
        print(f"  Linear features: {len(feature_results['linear_matches'])} matches")
        print(f"  Total matches: {len(feature_results['all_matches'])}")
        
        print(f"\nAlignment Quality:")
        print(f"  RMSE: {metrics['rmse']:.4f}m")
        print(f"  Median: {metrics['median']:.4f}m")
        print(f"  Centimeter accuracy: {metrics['pct_5cm']:.1f}% < 5cm")
        print(f"  Sub-meter accuracy: {metrics['pct_1m']:.1f}% < 1m")
        
        # Quality assessment
        if metrics['rmse'] < 0.1:
            quality = "EXCELLENT (cm-level)"
        elif metrics['rmse'] < 0.5:
            quality = "VERY GOOD (sub-meter)"
        elif metrics['rmse'] < 2.0:
            quality = "GOOD (meter-level)"
        else:
            quality = "ACCEPTABLE"
        
        print(f"  Quality: {quality}")
        
        print(f"\nComparison with original ICP:")
        original_rmse = 32.166  # From minimal notebook results
        improvement = original_rmse / metrics['rmse']
        print(f"  Original RMSE: {original_rmse:.3f}m")
        print(f"  Improved RMSE: {metrics['rmse']:.4f}m")
        print(f"  Improvement: {improvement:.1f}x better")
        
        return final_aligned_points, final_transformation, metrics, feature_results
        
    except Exception as e:
        print(f"\nError in feature-based alignment: {e}")
        import traceback
        traceback.print_exc()
        return None, None, None, None

def compare_with_original():
    """Compare feature-based results with original ICP"""
    print("\n" + "="*60)
    print("COMPARISON WITH ORIGINAL METHODS")
    print("="*60)
    
    # Load original results for comparison
    try:
        original_aligned = np.load("../../../data/processed/icp_alignment_minimal/aligned_drone_points.npy")
        feature_aligned = np.load(f"{OUTPUT_DIR}/feature_aligned_points.npy")
        ifc_points = np.asarray(o3d.io.read_point_cloud(str(IFC_FILE)).points)
        
        print("\nEvaluating original ICP alignment...")
        original_metrics = evaluate_improved_alignment(None, original_aligned, ifc_points)
        
        print("\nEvaluating feature-based alignment...")
        feature_metrics = evaluate_improved_alignment(None, feature_aligned, ifc_points)
        
        # Comparison table
        print(f"\n{'Metric':<20} {'Original ICP':<15} {'Feature-based':<15} {'Improvement':<12}")
        print("-" * 65)
        
        metrics_to_compare = [
            ('RMSE (m)', 'rmse'),
            ('Median (m)', 'median'),
            ('< 1cm (%)', 'pct_1cm'),
            ('< 5cm (%)', 'pct_5cm'),
            ('< 10cm (%)', 'pct_10cm'),
            ('< 1m (%)', 'pct_1m')
        ]
        
        for name, key in metrics_to_compare:
            orig_val = original_metrics[key]
            feat_val = feature_metrics[key]
            
            if 'pct' in key:
                improvement = f"{feat_val/orig_val:.1f}x" if orig_val > 0 else "N/A"
            else:
                improvement = f"{orig_val/feat_val:.1f}x" if feat_val > 0 else "N/A"
            
            print(f"{name:<20} {orig_val:<15.4f} {feat_val:<15.4f} {improvement:<12}")
        
    except FileNotFoundError as e:
        print(f"Could not load comparison files: {e}")

def advanced_feature_analysis():
    """Advanced analysis of feature quality and distribution"""
    print("\n" + "="*40)
    print("ADVANCED FEATURE ANALYSIS")
    print("="*40)
    
    try:
        # Load saved features (would need to modify save_results to include features)
        print("Analyzing feature distribution and quality...")
        
        # This would require saving feature data in save_results function
        # For now, just print placeholder analysis
        print("Feature quality analysis:")
        print("  - Corner detection reliability")
        print("  - Planar surface consistency") 
        print("  - Linear feature continuity")
        print("  - Match confidence distribution")
        print("  - Geometric consistency checks")
        
    except Exception as e:
        print(f"Advanced analysis not available: {e}")

# Additional utility functions

def manual_feature_refinement(drone_points, ifc_points, manual_correspondences=None):
    """Allow manual refinement of feature matches"""
    print("\nManual Feature Refinement Mode")
    print("(This would allow interactive feature matching)")
    
    if manual_correspondences:
        print(f"Using {len(manual_correspondences)} manual correspondences")
        # Implementation would go here
        pass
    else:
        print("No manual correspondences provided")
        print("Recommend identifying 4-6 clear corresponding points:")
        print("  - Building corners")
        print("  - Foundation edges") 
        print("  - Distinctive structural elements")

def export_for_external_validation(aligned_points, transformation, output_dir):
    """Export results for validation in external software"""
    output_path = Path(output_dir)
    
    # Export transformation in different formats
    # 4x4 matrix
    np.savetxt(output_path / "transformation_4x4.txt", transformation, fmt='%.8f')
    
    # Rotation + translation
    rotation = transformation[:3, :3]
    translation = transformation[:3, 3]
    np.savetxt(output_path / "rotation_matrix.txt", rotation, fmt='%.8f')
    np.savetxt(output_path / "translation_vector.txt", translation, fmt='%.8f')
    
    # CloudCompare format (if needed)
    cc_format = np.zeros((4, 4))
    cc_format[:3, :3] = rotation.T  # CloudCompare uses transposed rotation
    cc_format[:3, 3] = -rotation.T @ translation
    cc_format[3, 3] = 1.0
    np.savetxt(output_path / "cloudcompare_transform.txt", cc_format, fmt='%.8f')
    
    print(f"Exported transformation matrices to {output_path}")

if __name__ == "__main__":
    # Run main pipeline
    aligned_points, transformation, metrics, feature_results = main()
    
    if aligned_points is not None:
        # Compare with original
        compare_with_original()
        
        # Advanced analysis
        advanced_feature_analysis()
        
        # Export for validation
        export_for_external_validation(aligned_points, transformation, OUTPUT_DIR)
        
        print(f"\n🎯 Feature-based alignment complete!")
        print(f"Check {OUTPUT_DIR} for all results and visualizations")
    else:
        print("\n❌ Feature-based alignment failed")

# Example usage for different scenarios:

def run_conservative_alignment():
    """Run with conservative parameters for challenging datasets"""
    global OUTPUT_DIR
    OUTPUT_DIR = "../../../data/processed/feature_aligned_conservative"
    
    # Override parameters for more robust alignment
    extractor = GeometricFeatureExtractor(voxel_size=1.0, min_points=100)
    matcher = FeatureMatcher(distance_threshold=5.0)
    
    print("Running conservative feature-based alignment...")
    # Run with modified parameters

def run_precision_alignment():
    """Run with precision parameters for high-quality datasets"""
    global OUTPUT_DIR  
    OUTPUT_DIR = "../../../data/processed/feature_aligned_precision"
    
    # Override for high precision
    extractor = GeometricFeatureExtractor(voxel_size=0.2, min_points=20)
    matcher = FeatureMatcher(distance_threshold=1.0)
    
    print("Running precision feature-based alignment...")
    # Run with precision parameters

# Validation functions

def validate_feature_matches(matches, transformation):
    """Validate quality of feature matches"""
    errors = []
    for match in matches:
        # Calculate reprojection error
        if 'drone_point' in match:
            source = match['drone_point']
            target = match['ifc_point']
        else:
            source = match['drone_centroid']
            target = match['ifc_centroid']
        
        source_h = np.append(source, 1.0)
        transformed = (transformation @ source_h)[:3]
        error = np.linalg.norm(transformed - target)
        errors.append(error)
    
    return {
        'mean_error': np.mean(errors),
        'max_error': np.max(errors),
        'std_error': np.std(errors),
        'errors': errors
    }

def geometric_consistency_check(matches):
    """Check geometric consistency of matches"""
    if len(matches) < 4:
        return {"status": "insufficient_matches"}
    
    # Check if matches preserve distances (rigid transformation)
    distances_drone = []
    distances_ifc = []
    
    for i in range(len(matches)):
        for j in range(i+1, len(matches)):
            if 'drone_point' in matches[i]:
                p1_drone = matches[i]['drone_point']
                p1_ifc = matches[i]['ifc_point']
            else:
                p1_drone = matches[i]['drone_centroid']
                p1_ifc = matches[i]['ifc_centroid']
                
            if 'drone_point' in matches[j]:
                p2_drone = matches[j]['drone_point']
                p2_ifc = matches[j]['ifc_point']
            else:
                p2_drone = matches[j]['drone_centroid']
                p2_ifc = matches[j]['ifc_centroid']
            
            dist_drone = np.linalg.norm(p1_drone - p2_drone)
            dist_ifc = np.linalg.norm(p1_ifc - p2_ifc)
            
            distances_drone.append(dist_drone)
            distances_ifc.append(dist_ifc)
    
    # Calculate scale consistency
    ratios = np.array(distances_ifc) / (np.array(distances_drone) + 1e-6)
    scale_consistency = np.std(ratios) / np.mean(ratios)
    
    return {
        "status": "checked",
        "scale_consistency": scale_consistency,
        "mean_scale": np.mean(ratios),
        "scale_std": np.std(ratios)
    }

# Feature-Only Quick Fix - Self-contained version
# This bypasses the problematic ICP and uses only feature-based transformation

import numpy as np
import open3d as o3d
from pathlib import Path
from scipy.spatial import cKDTree
from scipy.spatial.distance import cdist
import time

def quick_feature_alignment():
    """Simple feature-based alignment without complex imports"""
    print("=== QUICK FEATURE-ONLY ALIGNMENT ===")
    
    # Load data
    try:
        drone_points = np.load("../../../data/processed/icp_alignment_minimal/aligned_drone_points.npy")
        ifc_pcd = o3d.io.read_point_cloud("../../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply")
        ifc_points = np.asarray(ifc_pcd.points)
        
        print(f"Loaded drone: {len(drone_points):,} points")
        print(f"Loaded IFC: {len(ifc_points):,} points")
        
    except FileNotFoundError as e:
        print(f"File not found: {e}")
        return None, None, None
    
    # Simple FPFH-based feature alignment
    print("\nExtracting FPFH features...")
    
    # Create point clouds
    drone_pcd = o3d.geometry.PointCloud()
    drone_pcd.points = o3d.utility.Vector3dVector(drone_points)
    drone_pcd = drone_pcd.voxel_down_sample(0.5)
    
    ifc_pcd_ds = o3d.geometry.PointCloud()
    ifc_pcd_ds.points = o3d.utility.Vector3dVector(ifc_points)
    ifc_pcd_ds = ifc_pcd_ds.voxel_down_sample(1.0)
    
    print(f"Downsampled: {len(drone_pcd.points):,} drone, {len(ifc_pcd_ds.points):,} IFC")
    
    # Estimate normals
    drone_pcd.estimate_normals()
    ifc_pcd_ds.estimate_normals()
    
    # Extract FPFH features
    print("Computing FPFH features...")
    drone_fpfh = o3d.pipelines.registration.compute_fpfh_feature(
        drone_pcd, o3d.geometry.KDTreeSearchParamHybrid(radius=2.0, max_nn=100)
    )
    ifc_fpfh = o3d.pipelines.registration.compute_fpfh_feature(
        ifc_pcd_ds, o3d.geometry.KDTreeSearchParamHybrid(radius=2.0, max_nn=100)
    )
    
    # RANSAC feature matching
    print("RANSAC feature matching...")
    result = o3d.pipelines.registration.registration_ransac_based_on_feature_matching(
        drone_pcd, ifc_pcd_ds, drone_fpfh, ifc_fpfh,
        mutual_filter=True,
        max_correspondence_distance=2.0,
        estimation_method=o3d.pipelines.registration.TransformationEstimationPointToPoint(),
        ransac_n=3,
        checkers=[
            o3d.pipelines.registration.CorrespondenceCheckerBasedOnEdgeLength(0.9),
            o3d.pipelines.registration.CorrespondenceCheckerBasedOnDistance(2.0)
        ],
        criteria=o3d.pipelines.registration.RANSACConvergenceCriteria(100000, 0.999)
    )
    
    print(f"RANSAC fitness: {result.fitness:.6f}")
    print(f"RANSAC RMSE: {result.inlier_rmse:.6f}")
    
    # Apply transformation to full dataset
    print("Applying transformation to full dataset...")
    drone_h = np.ones((len(drone_points), 4))
    drone_h[:, :3] = drone_points
    aligned_points = (drone_h @ result.transformation.T)[:, :3]
    
    # Evaluate results
    print("Evaluating alignment...")
    metrics = evaluate_alignment_quick(aligned_points, ifc_points)
    
    # Save results
    output_dir = Path("../../../data/processed/feature_quick_aligned")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    np.save(output_dir / "quick_feature_aligned_points.npy", aligned_points)
    np.save(output_dir / "quick_feature_transformation.npy", result.transformation)
    
    # Save point cloud
    aligned_pcd = o3d.geometry.PointCloud()
    aligned_pcd.points = o3d.utility.Vector3dVector(aligned_points)
    o3d.io.write_point_cloud(str(output_dir / "quick_feature_aligned.ply"), aligned_pcd)
    
    print(f"Results saved to {output_dir}")
    
    return aligned_points, result.transformation, metrics

def evaluate_alignment_quick(aligned_points, ifc_points):
    """Quick evaluation of alignment quality"""
    # Sample for performance
    sample_size = 10000
    aligned_sample = aligned_points[np.random.choice(len(aligned_points), 
                                                   min(sample_size, len(aligned_points)), 
                                                   replace=False)]
    ifc_sample = ifc_points[np.random.choice(len(ifc_points), 
                                           min(sample_size, len(ifc_points)), 
                                           replace=False)]
    
    # Calculate distances
    tree = cKDTree(ifc_sample)
    distances, _ = tree.query(aligned_sample)
    
    # Metrics
    metrics = {
        'rmse': np.sqrt(np.mean(distances**2)),
        'median': np.median(distances),
        'mean': np.mean(distances),
        'pct_1cm': np.sum(distances < 0.01) / len(distances) * 100,
        'pct_5cm': np.sum(distances < 0.05) / len(distances) * 100,
        'pct_10cm': np.sum(distances < 0.10) / len(distances) * 100,
        'pct_50cm': np.sum(distances < 0.50) / len(distances) * 100,
        'pct_1m': np.sum(distances < 1.0) / len(distances) * 100,
        'pct_2m': np.sum(distances < 2.0) / len(distances) * 100
    }
    
    print(f"\n=== ALIGNMENT RESULTS ===")
    print(f"RMSE: {metrics['rmse']:.4f}m")
    print(f"Median: {metrics['median']:.4f}m")
    print(f"Mean: {metrics['mean']:.4f}m")
    
    print(f"\nAccuracy breakdown:")
    print(f"  < 1cm: {metrics['pct_1cm']:.1f}%")
    print(f"  < 5cm: {metrics['pct_5cm']:.1f}%") 
    print(f"  < 10cm: {metrics['pct_10cm']:.1f}%")
    print(f"  < 50cm: {metrics['pct_50cm']:.1f}%")
    print(f"  < 1m: {metrics['pct_1m']:.1f}%")
    print(f"  < 2m: {metrics['pct_2m']:.1f}%")
    
    return metrics

def simple_correspondence_alignment():
    """Simple alignment using manually identified correspondences"""
    print("\n=== SIMPLE CORRESPONDENCE ALIGNMENT ===")
    
    # Load data
    try:
        #drone_points = np.load("../../../data/processed/icp_alignment_minimal/aligned_drone_points.npy")
        drone_file = Path("../../../data/processed/trino_enel/ground_segmentation/ransac_pmf/trino_enel_nonground.ply")
        drone_pcd = o3d.io.read_point_cloud(str(drone_file))
        drone_points = np.asarray(drone_pcd.points)  # This is what you originally used in minimal notebook

        ifc_pcd = o3d.io.read_point_cloud("../../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply")
        ifc_points = np.asarray(ifc_pcd.points)
    except FileNotFoundError as e:
        print(f"File not found: {e}")
        return None, None, None
    
    # Get coordinate ranges to suggest correspondence points
    print("Dataset coordinate ranges:")
    
    drone_bounds = [np.min(drone_points, axis=0), np.max(drone_points, axis=0)]
    ifc_bounds = [np.min(ifc_points, axis=0), np.max(ifc_points, axis=0)]
    
    print(f"Drone: X={drone_bounds[0][0]:.0f}-{drone_bounds[1][0]:.0f}, "
          f"Y={drone_bounds[0][1]:.0f}-{drone_bounds[1][1]:.0f}, "
          f"Z={drone_bounds[0][2]:.0f}-{drone_bounds[1][2]:.0f}")
    
    print(f"IFC: X={ifc_bounds[0][0]:.0f}-{ifc_bounds[1][0]:.0f}, "
          f"Y={ifc_bounds[0][1]:.0f}-{ifc_bounds[1][1]:.0f}, "
          f"Z={ifc_bounds[0][2]:.0f}-{ifc_bounds[1][2]:.0f}")
    
    # Check if already aligned
    drone_center = np.mean(drone_points, axis=0)
    ifc_center = np.mean(ifc_points, axis=0)
    separation = np.linalg.norm(drone_center - ifc_center)
    
    print(f"\nCurrent separation: {separation:.2f}m")
    
    if separation < 10:
        print("✅ Datasets are already well-aligned spatially")
        print("The remaining error is likely due to geometric differences")
        
        # Calculate current alignment quality
        metrics = evaluate_alignment_quick(drone_points, ifc_points)
        
        if metrics['rmse'] < 5.0:
            print(f"\n🎯 Current alignment is actually quite good!")
            print(f"RMSE {metrics['rmse']:.2f}m is reasonable for drone-to-IFC comparison")
        
        return drone_points, np.eye(4), metrics
    
    else:
        print("⚠️ Datasets still have significant separation")
        print("Manual correspondences recommended:")
        
        # Suggest corner points for manual identification
        suggested_points = [
            [drone_bounds[0][0] + 100, drone_bounds[0][1] + 100, np.mean(drone_points[:, 2])],  # SW corner
            [drone_bounds[1][0] - 100, drone_bounds[0][1] + 100, np.mean(drone_points[:, 2])],  # SE corner  
            [drone_bounds[1][0] - 100, drone_bounds[1][1] - 100, np.mean(drone_points[:, 2])],  # NE corner
            [drone_bounds[0][0] + 100, drone_bounds[1][1] - 100, np.mean(drone_points[:, 2])],  # NW corner
        ]
        
        print("\nSuggested correspondence points to identify:")
        for i, pt in enumerate(suggested_points):
            print(f"  Point {i+1}: [{pt[0]:.0f}, {pt[1]:.0f}, {pt[2]:.0f}]")
        
        return None, None, None

def compare_all_methods():
    """Compare original vs feature-based results"""
    print("\n=== METHOD COMPARISON ===")
    
    try:
        # Original results
        original_aligned = np.load("../../../data/processed/icp_alignment_minimal/aligned_drone_points.npy")
        
        # Feature results (if available)
        feature_aligned = None
        try:
            feature_aligned = np.load("../../../data/processed/feature_quick_aligned/quick_feature_aligned_points.npy")
        except FileNotFoundError:
            print("Feature-aligned results not found - run quick_feature_alignment() first")
        
        # IFC points
        ifc_pcd = o3d.io.read_point_cloud("../../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply")
        ifc_points = np.asarray(ifc_pcd.points)
        
        print("Evaluating original alignment...")
        original_metrics = evaluate_alignment_quick(original_aligned, ifc_points)
        
        if feature_aligned is not None:
            print("\nEvaluating feature-based alignment...")
            feature_metrics = evaluate_alignment_quick(feature_aligned, ifc_points)
            
            # Comparison
            print(f"\n=== COMPARISON SUMMARY ===")
            print(f"{'Metric':<15} {'Original':<12} {'Feature':<12} {'Improvement':<12}")
            print("-" * 55)
            
            improvements = {
                'RMSE': original_metrics['rmse'] / feature_metrics['rmse'],
                'Median': original_metrics['median'] / feature_metrics['median'],
                '<1m': feature_metrics['pct_1m'] / max(original_metrics['pct_1m'], 0.1)
            }
            
            print(f"{'RMSE (m)':<15} {original_metrics['rmse']:<12.3f} {feature_metrics['rmse']:<12.3f} {improvements['RMSE']:<12.1f}x")
            print(f"{'Median (m)':<15} {original_metrics['median']:<12.3f} {feature_metrics['median']:<12.3f} {improvements['Median']:<12.1f}x")
            print(f"{'<1m (%)':<15} {original_metrics['pct_1m']:<12.1f} {feature_metrics['pct_1m']:<12.1f} {improvements['<1m']:<12.1f}x")
    
    except FileNotFoundError as e:
        print(f"Comparison failed: {e}")

def main():
    """Main execution"""
    print("Feature-Only Alignment - Quick Fix")
    print("=" * 50)
    
    # Try quick feature alignment
    aligned_points, transformation, metrics = quick_feature_alignment()
    
    if metrics is not None:
        # Compare with original
        print(f"\n{'='*50}")
        print("IMPROVEMENT ANALYSIS")
        print(f"{'='*50}")
        
        original_rmse = 32.166  # From your earlier results
        if metrics['rmse'] < original_rmse:
            improvement = original_rmse / metrics['rmse']
            print(f"🎉 SUCCESS!")
            print(f"Original RMSE: {original_rmse:.3f}m")
            print(f"Feature RMSE: {metrics['rmse']:.3f}m") 
            print(f"Improvement: {improvement:.1f}x better")
        else:
            print(f"❌ No improvement over original")
            print(f"Original: {original_rmse:.3f}m, Feature: {metrics['rmse']:.3f}m")
    
    # Try simple correspondence approach
    print(f"\n{'='*50}")
    simple_correspondence_alignment()
    
    # Compare all methods
    print(f"\n{'='*50}")
    compare_all_methods()

if __name__ == "__main__":
    main()