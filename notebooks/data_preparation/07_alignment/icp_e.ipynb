# Point Cloud Alignment: Drone to IFC
# Minimal implementation without emojis

import numpy as np
import open3d as o3d
import matplotlib.pyplot as plt
from pathlib import Path
from scipy.spatial import cKDTree
import json
import time
from datetime import datetime

# Configuration
DRONE_FILE = "../../../data/processed/trino_enel/ground_segmentation/ransac_pmf/trino_enel_nonground.ply"
IFC_FILE = "../../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply"
OUTPUT_DIR = "../../../data/processed/icp_alignment_minimal"

def load_point_clouds(drone_path, ifc_path):
    """Load drone and IFC point clouds"""
    print("Loading point clouds...")
    
    # Load drone points
    drone_pcd = o3d.io.read_point_cloud(str(drone_path))
    drone_points = np.asarray(drone_pcd.points)
    
    # Load IFC points  
    ifc_pcd = o3d.io.read_point_cloud(str(ifc_path))
    ifc_points = np.asarray(ifc_pcd.points)
    
    print(f"Drone points: {len(drone_points):,}")
    print(f"IFC points: {len(ifc_points):,}")
    
    return drone_points, ifc_points

def analyze_coordinates(drone_points, ifc_points):
    """Analyze coordinate systems and calculate offsets"""
    print("\nAnalyzing coordinate systems...")
    
    # Calculate statistics
    drone_stats = {
        'min': np.min(drone_points, axis=0),
        'max': np.max(drone_points, axis=0), 
        'mean': np.mean(drone_points, axis=0),
        'median': np.median(drone_points, axis=0)
    }
    
    ifc_stats = {
        'min': np.min(ifc_points, axis=0),
        'max': np.max(ifc_points, axis=0),
        'mean': np.mean(ifc_points, axis=0),
        'median': np.median(ifc_points, axis=0)
    }
    
    # Calculate offsets
    xy_offset = drone_stats['mean'][:2] - ifc_stats['mean'][:2]
    z_offset = drone_stats['mean'][2] - ifc_stats['mean'][2]
    total_offset = np.linalg.norm(drone_stats['mean'] - ifc_stats['mean'])
    
    print(f"XY offset: {np.linalg.norm(xy_offset):.1f}m")
    print(f"Z offset: {abs(z_offset):.1f}m") 
    print(f"Total separation: {total_offset:.1f}m")
    
    # Check overlap
    drone_bounds = [drone_stats['min'], drone_stats['max']]
    ifc_bounds = [ifc_stats['min'], ifc_stats['max']]
    overlap_min = np.maximum(drone_bounds[0], ifc_bounds[0])
    overlap_max = np.minimum(drone_bounds[1], ifc_bounds[1])
    has_overlap = np.all(overlap_min < overlap_max)
    
    print(f"Spatial overlap: {has_overlap}")
    
    return {
        'drone_stats': drone_stats,
        'ifc_stats': ifc_stats,
        'offsets': {'xy': xy_offset, 'z': z_offset, 'total': total_offset},
        'has_overlap': has_overlap
    }

def coordinate_system_correction(drone_points, ifc_points, analysis):
    """Apply coordinate system correction"""
    print("\nApplying coordinate system correction...")
    
    # Test transformation strategies
    transformations = [
        {
            'name': 'Centroid Alignment',
            'offset': analysis['ifc_stats']['mean'] - analysis['drone_stats']['mean']
        },
        {
            'name': 'Ground Level Alignment', 
            'offset': np.array([
                -analysis['offsets']['xy'][0],
                -analysis['offsets']['xy'][1], 
                analysis['ifc_stats']['min'][2] - analysis['drone_stats']['min'][2]
            ])
        },
        {
            'name': 'Median Alignment',
            'offset': analysis['ifc_stats']['median'] - analysis['drone_stats']['median']
        }
    ]
    
    best_transform = None
    best_score = float('inf')
    
    for transform in transformations:
        drone_transformed = drone_points + transform['offset']
        
        # Calculate overlap score
        drone_min, drone_max = np.min(drone_transformed, axis=0), np.max(drone_transformed, axis=0)
        ifc_min, ifc_max = np.min(ifc_points, axis=0), np.max(ifc_points, axis=0)
        overlap_min = np.maximum(drone_min, ifc_min)
        overlap_max = np.minimum(drone_max, ifc_max)
        
        if np.all(overlap_min < overlap_max):
            overlap_volume = np.prod(overlap_max - overlap_min)
            drone_volume = np.prod(drone_max - drone_min)
            overlap_score = overlap_volume / drone_volume
            
            # Calculate RMSE
            sample_size = 5000
            drone_sample = drone_transformed[np.random.choice(len(drone_transformed), sample_size, replace=False)]
            ifc_sample = ifc_points[np.random.choice(len(ifc_points), sample_size, replace=False)]
            
            tree = cKDTree(ifc_sample)
            distances, _ = tree.query(drone_sample)
            rmse = np.sqrt(np.mean(distances**2))
            
            score = rmse / (overlap_score + 0.1)
            
            print(f"{transform['name']}: RMSE={rmse:.2f}m, Overlap={overlap_score:.1%}")
            
            if score < best_score:
                best_score = score
                best_transform = transform.copy()
                best_transform['rmse'] = rmse
                best_transform['overlap'] = overlap_score
                best_transform['transformed_points'] = drone_transformed
    
    if best_transform is None:
        raise ValueError("No suitable coordinate transformation found")
    
    print(f"Best method: {best_transform['name']} (RMSE: {best_transform['rmse']:.2f}m)")
    return best_transform

def fine_tune_z_alignment(transformed_points, ifc_points):
    """Fine-tune Z alignment using median matching"""
    print("\nFine-tuning Z alignment...")
    
    drone_z_median = np.median(transformed_points[:, 2])
    ifc_z_median = np.median(ifc_points[:, 2])
    z_correction = ifc_z_median - drone_z_median
    
    print(f"Z correction: {z_correction:.2f}m")
    
    fine_tuned_points = transformed_points.copy()
    fine_tuned_points[:, 2] += z_correction
    
    return fine_tuned_points

def icp_refinement(source_points, target_points):
    """Apply ICP refinement"""
    print("\nApplying ICP refinement...")
    
    # Prepare point clouds
    source_pcd = o3d.geometry.PointCloud()
    source_pcd.points = o3d.utility.Vector3dVector(source_points)
    
    target_pcd = o3d.geometry.PointCloud() 
    target_pcd.points = o3d.utility.Vector3dVector(target_points)
    
    # Downsample
    if len(source_points) > 100000:
        source_pcd = source_pcd.voxel_down_sample(0.5)
    if len(target_points) > 100000:
        target_pcd = target_pcd.voxel_down_sample(1.0)
    
    # Estimate normals
    source_pcd.estimate_normals()
    target_pcd.estimate_normals()
    
    # Crop to overlap region
    bbox = target_pcd.get_axis_aligned_bounding_box()
    bbox_min = np.asarray(bbox.min_bound)
    bbox_max = np.asarray(bbox.max_bound)
    bbox_size = bbox_max - bbox_min
    
    expanded_min = bbox_min - bbox_size * 0.1
    expanded_max = bbox_max + bbox_size * 0.1
    expanded_bbox = o3d.geometry.AxisAlignedBoundingBox(expanded_min, expanded_max)
    source_pcd = source_pcd.crop(expanded_bbox)
    
    print(f"ICP input: {len(source_pcd.points):,} source, {len(target_pcd.points):,} target")
    
    # Multi-scale ICP
    transformation = np.eye(4)
    scales = [3.0, 1.5, 0.75]
    iterations = [30, 50, 100]
    
    for i, (scale, iter_count) in enumerate(zip(scales, iterations)):
        try:
            result = o3d.pipelines.registration.registration_icp(
                source_pcd, target_pcd,
                max_correspondence_distance=scale,
                init=transformation,
                estimation_method=o3d.pipelines.registration.TransformationEstimationPointToPlane(),
                criteria=o3d.pipelines.registration.ICPConvergenceCriteria(
                    max_iteration=iter_count,
                    relative_fitness=1e-6,
                    relative_rmse=1e-6
                )
            )
            
            translation_mag = np.linalg.norm(result.transformation[:3, 3])
            print(f"  Scale {i+1}: Fitness={result.fitness:.4f}, RMSE={result.inlier_rmse:.4f}, Translation={translation_mag:.2f}m")
            
            if translation_mag > 20.0 or result.fitness < 0.01:
                print("  Stopping ICP due to poor convergence")
                break
                
            transformation = result.transformation
            
        except Exception as e:
            print(f"  ICP failed at scale {i+1}: {e}")
            break
    
    return transformation

def apply_final_transformation(drone_points, transformation):
    """Apply final transformation to full dataset"""
    print("\nApplying final transformation...")
    
    drone_h = np.ones((len(drone_points), 4))
    drone_h[:, :3] = drone_points
    final_points = (drone_h @ transformation.T)[:, :3]
    
    return final_points

def evaluate_alignment(aligned_points, ifc_points):
    """Evaluate final alignment quality"""
    print("\nEvaluating alignment quality...")
    
    # Sample for performance
    sample_size = 10000
    aligned_sample = aligned_points[np.random.choice(len(aligned_points), 
                                                   min(sample_size, len(aligned_points)), 
                                                   replace=False)]
    ifc_sample = ifc_points[np.random.choice(len(ifc_points), 
                                           min(sample_size, len(ifc_points)), 
                                           replace=False)]
    
    # Calculate distances
    tree = cKDTree(ifc_sample)
    distances, _ = tree.query(aligned_sample)
    
    # Metrics
    rmse = np.sqrt(np.mean(distances**2))
    mean_dist = np.mean(distances)
    median_dist = np.median(distances)
    
    pct_excellent = np.sum(distances < 0.5) / len(distances) * 100
    pct_good = np.sum(distances < 1.0) / len(distances) * 100
    pct_acceptable = np.sum(distances < 2.0) / len(distances) * 100
    
    print(f"RMSE: {rmse:.3f}m")
    print(f"Median distance: {median_dist:.3f}m")
    print(f"Points < 0.5m: {pct_excellent:.1f}%")
    print(f"Points < 1.0m: {pct_good:.1f}%")
    print(f"Points < 2.0m: {pct_acceptable:.1f}%")
    
    return {
        'rmse': rmse,
        'mean_distance': mean_dist,
        'median_distance': median_dist,
        'pct_excellent': pct_excellent,
        'pct_good': pct_good,
        'pct_acceptable': pct_acceptable
    }

def save_results(aligned_points, transformation, metrics, output_dir):
    """Save alignment results"""
    print(f"\nSaving results to {output_dir}...")
    
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    # Save point cloud
    aligned_pcd = o3d.geometry.PointCloud()
    aligned_pcd.points = o3d.utility.Vector3dVector(aligned_points)
    o3d.io.write_point_cloud(str(output_path / "aligned_drone.ply"), aligned_pcd)
    
    # Save transformation matrix
    np.save(output_path / "transformation_matrix.npy", transformation)
    
    # Save points as numpy array
    np.save(output_path / "aligned_drone_points.npy", aligned_points)
    
    # Save metrics
    report = {
        'timestamp': datetime.now().isoformat(),
        'metrics': {k: float(v) for k, v in metrics.items()},
        'transformation_matrix': transformation.tolist()
    }
    
    with open(output_path / "alignment_report.json", 'w') as f:
        json.dump(report, f, indent=2)
    
    print("Results saved successfully")

def main():
    """Main alignment pipeline"""
    print("Point Cloud Alignment Pipeline")
    print("=" * 40)
    
    # Load data
    drone_points, ifc_points = load_point_clouds(DRONE_FILE, IFC_FILE)
    
    # Analyze coordinates
    analysis = analyze_coordinates(drone_points, ifc_points)
    
    # Apply coordinate correction
    transform_result = coordinate_system_correction(drone_points, ifc_points, analysis)
    
    # Fine-tune Z alignment
    fine_tuned_points = fine_tune_z_alignment(transform_result['transformed_points'], ifc_points)
    
    # Apply ICP refinement
    icp_transformation = icp_refinement(fine_tuned_points, ifc_points)
    
    # Get final aligned points
    final_aligned_points = apply_final_transformation(fine_tuned_points, icp_transformation)
    
    # Evaluate results
    metrics = evaluate_alignment(final_aligned_points, ifc_points)
    
    # Calculate total transformation
    initial_transform = np.eye(4)
    initial_transform[:3, 3] = transform_result['offset']
    z_fine_tune = np.eye(4)
    z_fine_tune[2, 3] = np.median(ifc_points[:, 2]) - np.median(transform_result['transformed_points'][:, 2])
    total_transformation = icp_transformation @ z_fine_tune @ initial_transform
    
    # Save results
    save_results(final_aligned_points, total_transformation, metrics, OUTPUT_DIR)
    
    print("\n" + "=" * 40)
    print("ALIGNMENT COMPLETE")
    print(f"Final RMSE: {metrics['rmse']:.3f}m")
    print(f"Median distance: {metrics['median_distance']:.3f}m")
    print(f"Quality assessment: {'GOOD' if metrics['rmse'] < 10 else 'ACCEPTABLE' if metrics['rmse'] < 20 else 'POOR'}")

if __name__ == "__main__":
    main()

# Create two aligned datasets: XY-only (for heights) and Full (for spatial analysis)

import numpy as np
import open3d as o3d
from pathlib import Path
import json
from datetime import datetime

def create_dual_alignment():
    """Create both XY-aligned (preserving drone Z) and fully-aligned datasets"""
    
    print("=== CREATING DUAL ALIGNMENT DATASETS ===")
    
    # Load original ground-segmented drone data
    drone_file = "../../../data/processed/trino_enel/ground_segmentation/ransac_pmf/trino_enel_nonground.ply"
    drone_pcd = o3d.io.read_point_cloud(drone_file)
    drone_points_original = np.asarray(drone_pcd.points)
    
    # Load IFC data
    ifc_file = "../../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply"
    ifc_pcd = o3d.io.read_point_cloud(ifc_file)
    ifc_points = np.asarray(ifc_pcd.points)
    
    print(f"Original drone: {len(drone_points_original):,} points")
    print(f"Original IFC: {len(ifc_points):,} points")
    
    # Calculate coordinate analysis
    drone_stats = {
        'min': np.min(drone_points_original, axis=0),
        'max': np.max(drone_points_original, axis=0),
        'mean': np.mean(drone_points_original, axis=0)
    }
    
    ifc_stats = {
        'min': np.min(ifc_points, axis=0),
        'max': np.max(ifc_points, axis=0),
        'mean': np.mean(ifc_points, axis=0)
    }
    
    print(f"\nOriginal coordinates:")
    print(f"Drone: X={drone_stats['min'][0]:.0f}-{drone_stats['max'][0]:.0f}, "
          f"Y={drone_stats['min'][1]:.0f}-{drone_stats['max'][1]:.0f}, "
          f"Z={drone_stats['min'][2]:.1f}-{drone_stats['max'][2]:.1f}")
    
    print(f"IFC: X={ifc_stats['min'][0]:.0f}-{ifc_stats['max'][0]:.0f}, "
          f"Y={ifc_stats['min'][1]:.0f}-{ifc_stats['max'][1]:.0f}, "
          f"Z={ifc_stats['min'][2]:.1f}-{ifc_stats['max'][2]:.1f}")
    
    # Calculate offsets (from original minimal approach)
    xy_offset = drone_stats['mean'][:2] - ifc_stats['mean'][:2]
    z_ground_offset = ifc_stats['min'][2] - drone_stats['min'][2]
    
    print(f"\nOffsets calculated:")
    print(f"XY offset: [{xy_offset[0]:.2f}, {xy_offset[1]:.2f}]")
    print(f"Z ground offset: {z_ground_offset:.2f}m")
    
    # VERSION 1: XY-ALIGNED ONLY (PRESERVE DRONE Z FOR HEIGHT MEASUREMENTS)
    print(f"\n1. CREATING XY-ALIGNED VERSION (PRESERVING DRONE Z)")
    
    xy_only_offset = np.array([-xy_offset[0], -xy_offset[1], 0.0])  # Z = 0!
    drone_xy_aligned = drone_points_original + xy_only_offset
    
    print(f"XY-aligned drone Z-range: {np.min(drone_xy_aligned[:, 2]):.1f} to {np.max(drone_xy_aligned[:, 2]):.1f}")
    print(f"✅ Original drone heights preserved for pile measurements")
    
    # VERSION 2: FULLY ALIGNED (ORIGINAL APPROACH FOR SPATIAL ANALYSIS)
    print(f"\n2. CREATING FULLY-ALIGNED VERSION (MATCHING IFC Z)")
    
    full_offset = np.array([-xy_offset[0], -xy_offset[1], z_ground_offset])
    drone_fully_aligned = drone_points_original + full_offset
    
    print(f"Fully-aligned drone Z-range: {np.min(drone_fully_aligned[:, 2]):.1f} to {np.max(drone_fully_aligned[:, 2]):.1f}")
    print(f"IFC Z-range: {ifc_stats['min'][2]:.1f} to {ifc_stats['max'][2]:.1f}")
    print(f"✅ Z-coordinates match IFC for spatial overlap analysis")
    
    # Save both versions
    output_dir = Path("../../../data/processed/dual_alignment")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # XY-aligned version (for height measurements)
    np.save(output_dir / "drone_xy_aligned_z_preserved.npy", drone_xy_aligned)
    
    xy_aligned_pcd = o3d.geometry.PointCloud()
    xy_aligned_pcd.points = o3d.utility.Vector3dVector(drone_xy_aligned)
    o3d.io.write_point_cloud(str(output_dir / "drone_xy_aligned_z_preserved.ply"), xy_aligned_pcd)
    
    # Fully-aligned version (for spatial analysis)
    np.save(output_dir / "drone_fully_aligned.npy", drone_fully_aligned)
    
    fully_aligned_pcd = o3d.geometry.PointCloud()
    fully_aligned_pcd.points = o3d.utility.Vector3dVector(drone_fully_aligned)
    o3d.io.write_point_cloud(str(output_dir / "drone_fully_aligned.ply"), fully_aligned_pcd)
    
    # Create transformation matrices
    xy_transform = np.eye(4)
    xy_transform[:3, 3] = xy_only_offset
    
    full_transform = np.eye(4)
    full_transform[:3, 3] = full_offset
    
    np.save(output_dir / "xy_only_transformation.npy", xy_transform)
    np.save(output_dir / "full_transformation.npy", full_transform)
    
    # Create usage guide
    usage_guide = {
        'timestamp': datetime.now().isoformat(),
        'datasets': {
            'xy_aligned_z_preserved': {
                'file': 'drone_xy_aligned_z_preserved.npy',
                'use_case': 'Height measurements, pile analysis, construction monitoring',
                'z_range': f"{np.min(drone_xy_aligned[:, 2]):.1f} to {np.max(drone_xy_aligned[:, 2]):.1f}m",
                'description': 'Drone heights preserved relative to ground level (0-24m range)',
                'coordinate_system': 'XY aligned to IFC, Z in original drone reference'
            },
            'fully_aligned': {
                'file': 'drone_fully_aligned.npy', 
                'use_case': 'Spatial analysis, overlap studies, geometric comparison',
                'z_range': f"{np.min(drone_fully_aligned[:, 2]):.1f} to {np.max(drone_fully_aligned[:, 2]):.1f}m",
                'description': 'All coordinates aligned to IFC system',
                'coordinate_system': 'XYZ fully aligned to IFC reference system'
            }
        },
        'transformations': {
            'xy_only_offset': xy_only_offset.tolist(),
            'full_offset': full_offset.tolist(),
            'original_separation': {
                'xy': float(np.linalg.norm(xy_offset)),
                'z': float(abs(z_ground_offset)),
                'total': float(np.linalg.norm(drone_stats['mean'] - ifc_stats['mean']))
            }
        },
        'usage_recommendations': {
            'pile_height_measurement': 'Use drone_xy_aligned_z_preserved.npy',
            'foundation_progress': 'Use drone_xy_aligned_z_preserved.npy', 
            'spatial_overlap_analysis': 'Use drone_fully_aligned.npy',
            'construction_deviation_detection': 'Use drone_xy_aligned_z_preserved.npy',
            'as_built_vs_design_comparison': 'Use drone_fully_aligned.npy'
        }
    }
    
    with open(output_dir / "alignment_usage_guide.json", 'w') as f:
        json.dump(usage_guide, f, indent=2)
    
    print(f"\n✅ DUAL ALIGNMENT COMPLETE")
    print(f"📁 Results saved to: {output_dir}")
    print(f"\n📋 USAGE GUIDE:")
    print(f"🏗️  For HEIGHT MEASUREMENTS: Use 'drone_xy_aligned_z_preserved.npy'")
    print(f"   - Pile heights, construction progress, relative measurements")
    print(f"   - Z-range: {np.min(drone_xy_aligned[:, 2]):.1f} to {np.max(drone_xy_aligned[:, 2]):.1f}m (ground-relative)")
    
    print(f"\n🗺️  For SPATIAL ANALYSIS: Use 'drone_fully_aligned.npy'")
    print(f"   - Overlap studies, geometric comparison")
    print(f"   - Z-range: {np.min(drone_fully_aligned[:, 2]):.1f} to {np.max(drone_fully_aligned[:, 2]):.1f}m (absolute elevation)")
    
    return {
        'xy_aligned': drone_xy_aligned,
        'fully_aligned': drone_fully_aligned,
        'xy_transform': xy_transform,
        'full_transform': full_transform,
        'output_dir': output_dir
    }

def validate_dual_alignment():
    """Validate both alignment versions"""
    print("\n=== VALIDATING DUAL ALIGNMENT ===")
    
    try:
        # Load both versions
        xy_aligned = np.load("../../../data/processed/dual_alignment/drone_xy_aligned_z_preserved.npy")
        fully_aligned = np.load("../../../data/processed/dual_alignment/drone_fully_aligned.npy")
        
        # Load IFC for comparison
        ifc_pcd = o3d.io.read_point_cloud("../../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply")
        ifc_points = np.asarray(ifc_pcd.points)
        
        print("Validation results:")
        
        # Check XY alignment
        xy_center = np.mean(xy_aligned, axis=0)
        ifc_center = np.mean(ifc_points, axis=0)
        xy_separation = np.linalg.norm(xy_center[:2] - ifc_center[:2])
        
        print(f"XY-aligned version:")
        print(f"  XY separation from IFC: {xy_separation:.2f}m ({'✅ Good' if xy_separation < 10 else '❌ Poor'})")
        print(f"  Z-range preserved: {np.min(xy_aligned[:, 2]):.1f} to {np.max(xy_aligned[:, 2]):.1f}m")
        
        # Check full alignment
        full_center = np.mean(fully_aligned, axis=0)
        full_separation = np.linalg.norm(full_center - ifc_center)
        
        print(f"\nFully-aligned version:")
        print(f"  3D separation from IFC: {full_separation:.2f}m ({'✅ Good' if full_separation < 10 else '❌ Poor'})")
        print(f"  Z-range: {np.min(fully_aligned[:, 2]):.1f} to {np.max(fully_aligned[:, 2]):.1f}m")
        print(f"  IFC Z-range: {np.min(ifc_points[:, 2]):.1f} to {np.max(ifc_points[:, 2]):.1f}m")
        
        # Z-overlap check
        z_overlap = max(0, min(np.max(fully_aligned[:, 2]), np.max(ifc_points[:, 2])) - 
                            max(np.min(fully_aligned[:, 2]), np.min(ifc_points[:, 2])))
        print(f"  Z-overlap: {z_overlap:.1f}m ({'✅ Good' if z_overlap > 5 else '❌ Poor'})")
        
    except FileNotFoundError:
        print("❌ Dual alignment files not found. Run create_dual_alignment() first.")

def example_pile_height_workflow():
    """Example workflow for pile height measurement using XY-aligned data"""
    print("\n=== EXAMPLE: PILE HEIGHT MEASUREMENT WORKFLOW ===")
    
    try:
        # Load XY-aligned data (preserves drone heights)
        drone_xy_aligned = np.load("../../../data/processed/dual_alignment/drone_xy_aligned_z_preserved.npy")
        
        # Load IFC for pile locations (XY coordinates)
        ifc_pcd = o3d.io.read_point_cloud("../../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply")
        ifc_points = np.asarray(ifc_pcd.points)
        
        print("Pile height measurement workflow:")
        print("1. ✅ Use drone_xy_aligned_z_preserved.npy (XY aligned, Z preserved)")
        print("2. ✅ Extract pile XY locations from IFC model")
        print("3. ✅ For each pile location:")
        print("   - Find drone points within 2-3m radius")
        print("   - Extract height values (Z-coordinates)")
        print("   - Calculate pile top height, mean height, etc.")
        print("4. ✅ Heights are in original drone reference (ground-relative)")
        print("5. ✅ Compare with design pile heights")
        
        # Example pile location (you'd get these from IFC)
        example_pile_xy = [436000, 5011500]  # Example coordinates
        
        # Find drone points near this pile
        distances_xy = np.sqrt((drone_xy_aligned[:, 0] - example_pile_xy[0])**2 + 
                              (drone_xy_aligned[:, 1] - example_pile_xy[1])**2)
        
        pile_points = drone_xy_aligned[distances_xy < 3.0]  # 3m radius
        
        if len(pile_points) > 0:
            pile_height_stats = {
                'max_height': np.max(pile_points[:, 2]),
                'mean_height': np.mean(pile_points[:, 2]),
                'median_height': np.median(pile_points[:, 2]),
                'min_height': np.min(pile_points[:, 2]),
                'point_count': len(pile_points)
            }
            
            print(f"\nExample pile at [{example_pile_xy[0]}, {example_pile_xy[1]}]:")
            print(f"  Points found: {pile_height_stats['point_count']}")
            print(f"  Height range: {pile_height_stats['min_height']:.1f} to {pile_height_stats['max_height']:.1f}m")
            print(f"  Mean height: {pile_height_stats['mean_height']:.1f}m")
            print(f"  ✅ These are REAL construction heights (ground-relative)")
        
    except FileNotFoundError:
        print("❌ XY-aligned file not found. Run create_dual_alignment() first.")

if __name__ == "__main__":
    # Create both alignment versions
    result = create_dual_alignment()
    
    # Validate the results
    validate_dual_alignment()
    
    # Show example usage
    example_pile_height_workflow()