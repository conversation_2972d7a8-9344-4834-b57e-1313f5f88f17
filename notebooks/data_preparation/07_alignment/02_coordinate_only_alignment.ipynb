# Parameters (Papermill)
ground_method = "ransac_pmf"  # Ground segmentation method
site_name = "trino_enel"
output_dir = "../../../data/processed/coordinate_alignment_corrected"
save_results = True
quality_sample_size = 5000  # For quality assessment sampling

import numpy as np
import pandas as pd
import open3d as o3d
import laspy
from pathlib import Path
import json
from scipy.spatial import cKDTree
import warnings
warnings.filterwarnings('ignore')

print("=== CORRECTED COORDINATE-ONLY ALIGNMENT ===")
print(f"Ground method: {ground_method}")
print(f"Site: {site_name}")
print(f"Output directory: {output_dir}")
print(f"Using metadata coordinates (corrected approach)")

# Define file paths
drone_file = f"../../../data/processed/{site_name}/ground_segmentation/{ground_method}/{site_name}_nonground.ply"
ifc_metadata_file = f"../../../data/processed/{site_name}/ifc_metadata/GRE.EEC.S.00.IT.P.14353.00.265_enhanced_metadata.csv"
ifc_pointcloud_file = f"../../../data/processed/{site_name}/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply"  # For comparison

print("Loading data with corrected approach...")
print(f"Drone file: {drone_file}")
print(f"IFC metadata file: {ifc_metadata_file}")
print(f"IFC point cloud file: {ifc_pointcloud_file} (for comparison)")



def load_drone_points(drone_path):
    """Load drone point cloud"""
    drone_file = Path(drone_path)
    
    if not drone_file.exists():
        raise FileNotFoundError(f"Drone file not found: {drone_path}")
    
    if drone_file.suffix.lower() == ".las":
        drone_las = laspy.read(drone_file)
        drone_points = drone_las.xyz
    elif drone_file.suffix.lower() == ".ply":
        drone_pcd = o3d.io.read_point_cloud(str(drone_file))
        drone_points = np.asarray(drone_pcd.points)
    else:
        raise ValueError("Unsupported drone file format. Use .las or .ply")
    
    print(f"Loaded drone scan: {drone_points.shape[0]:,} points")
    return drone_points

# Load data using corrected approach
drone_points = load_drone_points(drone_file)

def load_ifc_points_from_metadata(metadata_csv_path):
    """Load IFC coordinates from metadata CSV (corrected approach)"""
    metadata_file = Path(metadata_csv_path)
    
    if not metadata_file.exists():
        raise FileNotFoundError(f"IFC metadata file not found: {metadata_csv_path}")
    
    # Load metadata
    df = pd.read_csv(metadata_file)
    print(f"Loaded IFC metadata: {len(df):,} records")
    
    # Extract coordinates
    coord_cols = ['X', 'Y', 'Z']
    if not all(col in df.columns for col in coord_cols):
        raise ValueError(f"Missing coordinate columns. Found: {list(df.columns)}")
    
    # Get valid coordinates
    valid_coords = df[coord_cols].dropna()
    ifc_points = valid_coords.values
    
    print(f"Valid IFC coordinates: {len(ifc_points):,} points")
    print(f"Coordinate ranges:")
    for i, col in enumerate(coord_cols):
        print(f"  {col}: {ifc_points[:, i].min():.2f} to {ifc_points[:, i].max():.2f}")
    
    return ifc_points

print(f"\n=== USING METADATA COORDINATES (CORRECTED APPROACH) ===")
ifc_points = load_ifc_points_from_metadata(ifc_metadata_file)


def load_ifc_points_from_pointcloud(ifc_ply_path):
    """Load IFC point cloud (for comparison)"""
    ifc_file = Path(ifc_ply_path)
    
    if not ifc_file.exists():
        print(f"Warning: IFC point cloud file not found: {ifc_ply_path}")
        return None
    
    ifc_pcd = o3d.io.read_point_cloud(str(ifc_file))
    ifc_points = np.asarray(ifc_pcd.points)
    
    print(f"Loaded IFC point cloud: {ifc_points.shape[0]:,} points (for comparison)")
    return ifc_points

print(f"\n=== POINT CLOUD COMPARISON (VERIFICATION) ===")
ifc_points_pointcloud = load_ifc_points_from_pointcloud(ifc_pointcloud_file)




try:
    if ifc_points_pointcloud is not None:
        # Compare centers
        metadata_center = np.mean(ifc_points_original, axis=0)
        pointcloud_center = np.mean(ifc_points_pointcloud, axis=0)
        center_diff = np.linalg.norm(metadata_center - pointcloud_center)
        
        print(f"Metadata center: [{metadata_center[0]:.2f}, {metadata_center[1]:.2f}, {metadata_center[2]:.2f}]")
        print(f"Point cloud center: [{pointcloud_center[0]:.2f}, {pointcloud_center[1]:.2f}, {pointcloud_center[2]:.2f}]")
        print(f"Center difference: {center_diff:.2f}m")
        
        if center_diff > 100:
            print(f"Large difference detected - using metadata coordinates")
        else:
            print(f"Centers are similar - both approaches would work")
            
except Exception as e:
    print(f"Point cloud comparison failed: {e}")
    print(f"Proceeding with metadata coordinates only")

print(f"\nData loading complete:")
print(f"  Drone points: {len(drone_points):,}")
print(f"  IFC points (from metadata): {len(ifc_points):,}")

def analyze_alignment(drone_pts, ifc_pts):
    drone_center = np.mean(drone_pts, axis=0)
    ifc_center = np.mean(ifc_pts, axis=0)
    offset = ifc_center - drone_center
    
    print("Point Cloud Analysis:")
    print(f"  Drone center: {np.round(drone_center, 2)}")
    print(f"  IFC center:   {np.round(ifc_center, 2)}")
    print(f"  Required offset: {np.round(offset, 2)}")
    print(f"  Total separation: {np.linalg.norm(offset):.2f}m")
    
    return offset

offset_vector = analyze_alignment(drone_points, ifc_points)
print(f"\nRequired coordinate offset: [{offset_vector[0]:.2f}, {offset_vector[1]:.2f}, {offset_vector[2]:.2f}]")


def apply_coordinate_alignment(drone_pts, offset):
    aligned_pts = drone_pts + offset
    
    # Verify alignment
    aligned_center = np.mean(aligned_pts, axis=0)
    ifc_center = np.mean(ifc_points, axis=0)
    final_error = np.linalg.norm(aligned_center - ifc_center)
    
    print(f"Alignment Results:")
    print(f"  Final centroid error: {final_error:.6f}m")
    print(f"  Status: {'SUCCESS' if final_error < 0.1 else 'CHECK REQUIRED'}")
    
    return aligned_pts

# Apply alignment
drone_aligned = apply_coordinate_alignment(drone_points, offset_vector)

def assess_alignment_quality(drone_aligned, ifc_pts, sample_size=10000):
    # Sample for performance
    n_drone = min(sample_size, len(drone_aligned))
    n_ifc = min(sample_size, len(ifc_pts))
    
    drone_sample = drone_aligned[np.random.choice(len(drone_aligned), n_drone, replace=False)]
    ifc_sample = ifc_pts[np.random.choice(len(ifc_pts), n_ifc, replace=False)]
    
    # Nearest neighbor distances
    tree = cKDTree(ifc_sample)
    distances, _ = tree.query(drone_sample)
    
    # Calculate metrics
    rmse = np.sqrt(np.mean(distances**2))
    median_dist = np.median(distances)
    
    # Quality thresholds
    excellent_pct = np.sum(distances < 0.5) / len(distances) * 100
    good_pct = np.sum(distances < 2.0) / len(distances) * 100
    acceptable_pct = np.sum(distances < 10.0) / len(distances) * 100
    
    print("Quality Assessment:")
    print(f"  RMSE: {rmse:.2f}m")
    print(f"  Median distance: {median_dist:.2f}m")
    print(f"  Excellent (<0.5m): {excellent_pct:.1f}%")
    print(f"  Good (<2.0m): {good_pct:.1f}%")
    print(f"  Acceptable (<10.0m): {acceptable_pct:.1f}%")
    
    return {
        'rmse': rmse,
        'median_distance': median_dist,
        'excellent_pct': excellent_pct,
        'good_pct': good_pct,
        'acceptable_pct': acceptable_pct,
        'method': 'coordinate_only'
    }

quality_results = assess_alignment_quality(drone_aligned, ifc_points)


if save_results:
    output_path = Path(output_dir) / ground_method
    output_path.mkdir(parents=True, exist_ok=True)
    
    # Save aligned point cloud
    aligned_pcd = o3d.geometry.PointCloud()
    aligned_pcd.points = o3d.utility.Vector3dVector(drone_aligned)
    aligned_file = output_path / f"{site_name}_coordinate_aligned.ply"
    o3d.io.write_point_cloud(str(aligned_file), aligned_pcd)
    
    # Save transformation parameters
    transform_data = {
        'offset_vector': offset_vector.tolist(),
        'quality_metrics': quality_results
    }
    
    with open(output_path / f"{site_name}_coordinate_transform.json", 'w') as f:
        json.dump(transform_data, f, indent=2)
    
    print(f"Results saved to: {output_path}")

print("=== COORDINATE-ONLY ALIGNMENT COMPLETE ===")
print(f"Final RMSE: {quality_results['rmse']:.2f}m")
print(f"Method: Robust and reliable for construction monitoring")


!ls -lh ../../../data/processed/coordinate_alignment_corrected/ransac_pmf