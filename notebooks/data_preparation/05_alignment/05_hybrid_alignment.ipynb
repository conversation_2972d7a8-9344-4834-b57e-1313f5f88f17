{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"tags": ["parameters"]}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:__main__:Configuration:\n", "INFO:__main__:  Ground Method: csf\n", "INFO:__main__:  Source File: ../../data/processed/trino_enel/ground_segmentation/csf/trino_enel_nonground.ply\n", "INFO:__main__:  Target File: GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\n", "INFO:__main__:  Output Dir: ../../data/output_runs/alignment_testing/csf\n", "INFO:__main__:  MLflow Run: hybrid_csf_trino_enel\n"]}], "source": ["# Papermill parameters - will be overridden during execution\n", "ground_method = \"csf\"  # Default: csf, pmf, ransac, ransac_pmf\n", "site_name = \"trino_enel\"\n", "project_type = \"trino_enel\"\n", "\n", "# Auto-discover ground segmentation file based on method\n", "import os\n", "from pathlib import Path\n", "\n", "def get_ground_file(method, base_path=\"../../data/processed/trino_enel/ground_segmentation\"):\n", "    \"\"\"Get ground segmentation file for specified method\"\"\"\n", "    \n", "    base_path = Path(base_path)\n", "    method_path = base_path / method\n", "    \n", "    # List of common filename patterns to check - updated for actual file structure\n", "    # Use NON-GROUND points for infrastructure alignment (structures, not terrain)\n", "    patterns = [\n", "        \"trino_enel_nonground.ply\",  # Exact match for your files - STRUCTURES\n", "        \"*nonground*.ply\",\n", "        \"*nonground*.las\"\n", "    ]\n", "    \n", "    if not method_path.exists():\n", "        logger.info(f\"Warning: Method path does not exist: {method_path}\")\n", "        return None\n", "    \n", "    for pattern in patterns:\n", "        matching_files = list(method_path.glob(pattern))\n", "        if matching_files:\n", "            return matching_files[0]  # Return first match as Path object\n", "    \n", "    # Fallback to default naming\n", "    fallback_file = method_path / f\"{method}_ground_points.ply\"\n", "    return fallback_file if fallback_file.exists() else None\n", "\n", "# Set source file based on ground method\n", "source_file = get_ground_file(ground_method)\n", "target_file = \"GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\"  # Data-driven IFC point cloud file\n", "\n", "# Hybrid parameters\n", "neural_network_weight = 0.6\n", "icp_weight = 0.4\n", "max_iterations = 50\n", "tolerance = 1e-6\n", "num_points = 1024\n", "icp_max_iterations = 20\n", "icp_tolerance = 1e-6\n", "icp_voxel_size = 0.02\n", "use_downsampling = True\n", "\n", "# Output configuration\n", "output_dir = f\"../../data/output_runs/alignment_testing/{ground_method}\"\n", "mlflow_experiment_name = f\"alignment_hybrid_{ground_method}\"\n", "mlflow_run_name = f\"hybrid_{ground_method}_{site_name}\"  # Hybrid-specific run name\n", "\n", "# Execution flags\n", "save_intermediate = True\n", "enable_visualization = True\n", "save_results = True\n", "\n", "# Configure logging\n", "import logging\n", "logging.basicConfig(level=logging.INFO)\n", "logger = logging.getLogger(__name__)\n", "\n", "# Create output directory\n", "os.makedirs(output_dir, exist_ok=True)\n", "\n", "logger.info(f\"Configuration:\")\n", "logger.info(f\"  Ground Method: {ground_method}\")\n", "logger.info(f\"  Source File: {source_file}\")\n", "logger.info(f\"  Target File: {target_file}\")\n", "logger.info(f\"  Output Dir: {output_dir}\")\n", "logger.info(f\"  MLflow Run: {mlflow_run_name}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## File Path Validation"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:__main__:File Validation for csf:\n", "INFO:__main__:  Source: ../../data/processed/trino_enel/ground_segmentation/csf/trino_enel_nonground.ply\n", "INFO:__main__:  Target: ../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\n", "INFO:__main__:File validation passed - ready to proceed\n", "INFO:__main__:Final source file: ../../data/processed/trino_enel/ground_segmentation/csf/trino_enel_nonground.ply\n", "INFO:__main__:Final target file: ../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\n"]}], "source": ["# File Path Validation - Validate that required files exist\n", "import os\n", "from pathlib import Path\n", "\n", "def validate_file_paths(source_file, target_file, ground_method):\n", "    \"\"\"Validate that required files exist\"\"\"\n", "    \n", "    # Define base paths\n", "    base_path = Path(\"../../data/processed/trino_enel\")\n", "    ground_base = base_path / \"ground_segmentation\"\n", "    ifc_base = base_path / \"ifc_pointclouds\"\n", "    \n", "    # Check for source file in multiple locations\n", "    if isinstance(source_file, Path):\n", "        source_paths = [source_file]\n", "    else:\n", "        source_paths = [\n", "            ground_base / source_file,\n", "            ground_base / ground_method / source_file,\n", "            base_path / \"ground_segmentation\" / source_file\n", "        ]\n", "    \n", "    source_found = None\n", "    for path in source_paths:\n", "        if path.exists():\n", "            source_found = path\n", "            break\n", "    \n", "    # Check target file\n", "    target_paths = [\n", "        ifc_base / target_file,\n", "        base_path / \"ifc_pointclouds\" / target_file,\n", "        base_path / \"ifc_metadata\" / target_file,  # Also check IFC metadata folder\n", "        base_path / target_file\n", "    ]\n", "    \n", "    target_found = None\n", "    for path in target_paths:\n", "        if path.exists():\n", "            target_found = path\n", "            break\n", "    \n", "    # Report results\n", "    logger.info(f\"File Validation for {ground_method}:\")\n", "    \n", "    if source_found:\n", "        logger.info(f\"  Source: {source_found}\")\n", "    else:\n", "        logger.info(f\"  Source: {source_file} not found\")\n", "        logger.info(f\"     Searched in:\")\n", "        for path in source_paths:\n", "            logger.info(f\"       {path}\")\n", "        \n", "        # List available files\n", "        logger.info(f\"     Available ground files:\")\n", "        if ground_base.exists():\n", "            for method_dir in ground_base.iterdir():\n", "                if method_dir.is_dir():\n", "                    for file in method_dir.glob(\"*.ply\"):\n", "                        logger.info(f\"       {method_dir.name}/{file.name}\")\n", "    \n", "    if target_found:\n", "        logger.info(f\"  Target: {target_found}\")\n", "    else:\n", "        logger.info(f\"  Target: {target_file} not found\")\n", "        logger.info(f\"     Searched in:\")\n", "        for path in target_paths:\n", "            logger.info(f\"       {path}\")\n", "    \n", "    # Update file paths if found\n", "    if source_found:\n", "        globals()['source_file'] = str(source_found)\n", "    if target_found:\n", "        globals()['target_file'] = str(target_found)\n", "    \n", "    return source_found is not None and target_found is not None\n", "\n", "# Run validation\n", "validation_passed = validate_file_paths(source_file, target_file, ground_method)\n", "\n", "if not validation_passed:\n", "    logger.info(\"FILE VALIDATION FAILED\")\n", "    logger.info(\"Please check file paths before proceeding\")\n", "else:\n", "    logger.info(f\"File validation passed - ready to proceed\")\n", "    logger.info(f\"Final source file: {source_file}\")\n", "    logger.info(f\"Final target file: {target_file}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Hybrid Neural Network + ICP Point Cloud Alignment\n", "\n", "This notebook implements a hybrid approach that combines neural network-based coarse alignment with ICP refinement for precise point cloud registration. The hybrid method leverages the strengths of both approaches to achieve superior alignment results.\n", "\n", "**Stage**: Alignment  \n", "**Input Data**: Source and target point clouds  \n", "**Output**: Aligned point cloud with combined transformation  \n", "**Method**: Neural Network (coarse) + ICP (fine-tuning)  \n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: December 2024  \n", "**Project**: Energy Inspection 3D\n", "\n", "## Hybrid Approach Benefits:\n", "1. **Robust Initialization**: Neural network provides good initial alignment even with poor starting positions\n", "2. **Precise Refinement**: ICP fine-tunes the alignment for maximum accuracy\n", "3. **Best of Both Worlds**: Combines global optimization with local precision\n", "4. **Challenging Scenarios**: <PERSON><PERSON> partial overlaps and significant misalignments\n", "\n", "## Process Overview:\n", "1. **Environment Setup**: Import libraries and configure hybrid parameters\n", "2. **Data Loading**: Load source and target point clouds\n", "3. **Neural Network Stage**: Coarse alignment using trained neural network\n", "4. **ICP Refinement Stage**: Fine-tuning with traditional ICP\n", "5. **Evaluation**: Performance metrics and quality assessment\n", "6. **Visualization**: Comprehensive alignment results\n", "7. **Export**: Save aligned point clouds and metadata"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Environment Setup\n", "\n", "Configure the environment with required libraries for hybrid alignment."]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["# Install required packages\n", "#!pip install tensorflow open3d matplotlib laspy transforms3d scipy pandas mlflow"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:__main__:Hybrid Neural Network + ICP Alignment Environment Initialized\n", "INFO:__main__:TensorFlow version: 2.19.0\n", "INFO:__main__:Open3D version: 0.19.0\n", "INFO:__main__:Analysis Date: 2025-07-05 18:54:20\n"]}], "source": ["# Import libraries\n", "import tensorflow as tf\n", "import numpy as np\n", "import os\n", "import matplotlib.pyplot as plt\n", "import open3d as o3d\n", "import laspy\n", "import logging\n", "import time\n", "from pathlib import Path\n", "from datetime import datetime\n", "from scipy.spatial import cKDTree\n", "import pandas as pd\n", "import json\n", "import transforms3d.euler as t3d\n", "import transforms3d.quaternions as t3d_quaternions\n", "\n", "# MLflow tracking\n", "import mlflow\n", "import mlflow.tensorflow\n", "import mlflow.keras\n", "MLFLOW_AVAILABLE = True\n", "\n", "# Set random seeds for reproducibility\n", "np.random.seed(42)\n", "tf.random.set_seed(42)\n", "\n", "logger.info(\"Hybrid Neural Network + ICP Alignment Environment Initialized\")\n", "logger.info(f\"TensorFlow version: {tf.__version__}\")\n", "logger.info(f\"Open3D version: {o3d.__version__}\")\n", "logger.info(f\"Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Configuration Parameters\n", "\n", "Define hybrid alignment parameters and configuration."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:__main__:Project: ENEL/Trino\n", "INFO:__main__:Input path: ../../data/ENEL/Trino/preprocessing\n", "INFO:__main__:Output path: ../../data/ENEL/Trino/alignment\n", "INFO:__main__:Models path: ../../data/ENEL/Trino/alignment/models\n", "INFO:__main__:Hybrid results path: ../../data/ENEL/Trino/alignment/hybrid_results\n"]}], "source": ["class HybridAlignmentConfig:\n", "    \"\"\"Configuration parameters for hybrid neural network + ICP alignment.\"\"\"\n", "    \n", "    # Data paths\n", "    PROJECT_TYPE = \"ENEL\"  # Options: \"ENEL\", \"USA\"\n", "    PROJECT_NAME = \"Trino\"  # ENEL: <PERSON>, <PERSON>, <PERSON>, Giorgio | USA: <PERSON>, <PERSON><PERSON><PERSON>, RES\n", "    \n", "    # Neural Network Parameters\n", "    NUM_POINTS = 1024  # Number of points for neural network\n", "    NN_MODEL_PATH = None  # Path to trained neural network model\n", "    \n", "    # ICP Parameters\n", "    ICP_MAX_ITERATIONS = 20  # Fewer iterations since we start with good alignment\n", "    ICP_TOLERANCE = 1e-6\n", "    ICP_VOXEL_SIZE = 0.02  # For downsampling before ICP\n", "    \n", "    # Hybrid workflow parameters\n", "    USE_DOWNSAMPLING = True  # Whether to downsample for ICP stage\n", "    SAVE_INTERMEDIATE = True  # Save intermediate results\n", "    \n", "    # Evaluation parameters\n", "    DISTANCE_THRESHOLD = 0.1  # For correspondence evaluation\n", "    \n", "    def __init__(self):\n", "        self.base_path = Path('../..')\n", "        self.data_path = self.base_path / 'data'\n", "        self.input_path = self.data_path / self.PROJECT_TYPE / self.PROJECT_NAME / 'preprocessing'\n", "        self.output_path = self.data_path / self.PROJECT_TYPE / self.PROJECT_NAME / 'alignment'\n", "        self.models_path = self.output_path / 'models'\n", "        self.hybrid_results_path = self.output_path / 'hybrid_results'\n", "        \n", "        # Create directories\n", "        self.output_path.mkdir(parents=True, exist_ok=True)\n", "        self.models_path.mkdir(parents=True, exist_ok=True)\n", "        self.hybrid_results_path.mkdir(parents=True, exist_ok=True)\n", "        \n", "        logger.info(f\"Project: {self.PROJECT_TYPE}/{self.PROJECT_NAME}\")\n", "        logger.info(f\"Input path: {self.input_path}\")\n", "        logger.info(f\"Output path: {self.output_path}\")\n", "        logger.info(f\"Models path: {self.models_path}\")\n", "        logger.info(f\"Hybrid results path: {self.hybrid_results_path}\")\n", "\n", "config = HybridAlignmentConfig()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Utility Functions\n", "\n", "Implement utility functions for point cloud processing and transformations."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["def normalize_point_cloud(points):\n", "    \"\"\"\n", "    Normalizes a point cloud to be centered at the origin and scaled within a unit sphere.\n", "    \"\"\"\n", "    # Center the point cloud at the origin\n", "    centroid = np.mean(points, axis=0)\n", "    centered = points - centroid\n", "    \n", "    # Scale the point cloud to fit inside a unit sphere\n", "    furthest_distance = np.max(np.linalg.norm(centered, axis=1))\n", "    if furthest_distance > 0:\n", "        normalized = centered / furthest_distance\n", "    else:\n", "        normalized = centered\n", "    \n", "    return normalized, centroid, furthest_distance"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["def downsample_voxel(points, voxel_size=0.02):\n", "    \"\"\"\n", "    Applies voxel grid downsampling to reduce point count while preserving structure.\n", "    \"\"\"\n", "    # Convert numpy array to Open3D point cloud\n", "    pcd = o3d.geometry.PointCloud()\n", "    pcd.points = o3d.utility.Vector3dVector(points)\n", "    \n", "    # Apply voxel downsampling\n", "    downsampled = pcd.voxel_down_sample(voxel_size=voxel_size)\n", "    \n", "    # Convert back to numpy array\n", "    return np.asarray(downsampled.points)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["def apply_transformation(points, R, t):\n", "    \"\"\"\n", "    Applies a transformation (rotation and translation) to a point cloud.\n", "    \"\"\"\n", "    # Apply rotation and translation\n", "    transformed_points = np.dot(points, R.T) + t\n", "    return transformed_points"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Neural Network Stage Implementation\n", "\n", "Implement the neural network coarse alignment stage."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["def neural_network_coarse_alignment(source_pc, target_pc, model=None, num_points=1024):\n", "    \"\"\"\n", "    Performs coarse alignment using a neural network.\n", "    \"\"\"\n", "    start_time = time.time()\n", "    \n", "    if model is not None:\n", "        logger.info(\"Using trained neural network model for coarse alignment...\")\n", "        \n", "        # Sample points if necessary\n", "        if source_pc.shape[0] > num_points:\n", "            source_indices = np.random.choice(source_pc.shape[0], num_points, replace=False)\n", "            source_sample = source_pc[source_indices]\n", "        else:\n", "            source_indices = np.random.choice(source_pc.shape[0], num_points, replace=True)\n", "            source_sample = source_pc[source_indices]\n", "        \n", "        if target_pc.shape[0] > num_points:\n", "            target_indices = np.random.choice(target_pc.shape[0], num_points, replace=False)\n", "            target_sample = target_pc[target_indices]\n", "        else:\n", "            target_indices = np.random.choice(target_pc.shape[0], num_points, replace=True)\n", "            target_sample = target_pc[target_indices]\n", "        \n", "        # Reshape for model input (add batch dimension)\n", "        source_input = np.expand_dims(source_sample, axis=0)\n", "        target_input = np.expand_dims(target_sample, axis=0)\n", "        \n", "        # Predict transformation\n", "        quaternion, translation = model.predict([source_input, target_input])\n", "        \n", "        # Convert quaternion to rotation matrix\n", "        quaternion = quaternion[0]  # Remove batch dimension\n", "        translation = translation[0]  # Remove batch dimension\n", "        \n", "        # Convert quaternion to rotation matrix\n", "        R_neural = t3d_quaternions.quat2mat(quaternion)\n", "        t_neural = translation\n", "        \n", "    else:\n", "        logger.info(\"No trained model available. Using synthetic coarse alignment...\")\n", "        \n", "        # Generate a reasonable coarse alignment (simulate neural network output)\n", "        # This would be replaced with actual neural network prediction\n", "        angle = np.radians(10)  # 10 degree rotation\n", "        R_neural = np.array([\n", "            [np.cos(angle), -np.sin(angle), 0],\n", "            [np.sin(angle), np.cos(angle), 0],\n", "            [0, 0, 1]\n", "        ])\n", "        t_neural = np.array([0.15, 0.08, 0.03])  # Small translation\n", "        \n", "        # Add some noise to simulate imperfect neural network prediction\n", "        noise_rotation = np.random.normal(0, 0.02, (3, 3))\n", "        noise_translation = np.random.normal(0, 0.01, 3)\n", "        \n", "        R_neural = R_neural + noise_rotation\n", "        t_neural = t_neural + noise_translation\n", "        \n", "        # Ensure R_neural is still a valid rotation matrix\n", "        U, _, Vt = np.linalg.svd(R_neural)\n", "        R_neural = np.dot(U, Vt)\n", "        if np.linalg.det(R_neural) < 0:\n", "            R_neural[:, -1] *= -1\n", "    \n", "    # Apply the neural network transformation to the entire source point cloud\n", "    aligned_source = apply_transformation(source_pc, R_neural, t_neural)\n", "    \n", "    neural_time = time.time() - start_time\n", "    \n", "    logger.info(f\"Neural network coarse alignment completed in {neural_time:.4f} seconds\")\n", "    logger.info(f\"Neural network rotation matrix:\\n{R_neural}\")\n", "    logger.info(f\"Neural network translation vector: {t_neural}\")\n", "    \n", "    return aligned_source, R_neural, t_neural, neural_time"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. ICP Refinement Stage Implementation\n", "\n", "Implement the ICP refinement stage for precise alignment."]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["def nearest_neighbor(source, target):\n", "    \"\"\"\n", "    Find nearest neighbors between source and target point clouds.\n", "    \"\"\"\n", "    tree = cKDTree(target)\n", "    distances, indices = tree.query(source)\n", "    return distances, indices"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["def best_fit_transform(source, target):\n", "    \"\"\"\n", "    Calculates the least-squares best-fit transform between corresponding 3D points.\n", "    \"\"\"\n", "    assert source.shape == target.shape, \"Source and target must have the same shape\"\n", "    \n", "    # Center both point clouds\n", "    source_centroid = np.mean(source, axis=0)\n", "    target_centroid = np.mean(target, axis=0)\n", "    source_centered = source - source_centroid\n", "    target_centered = target - target_centroid\n", "    \n", "    # Compute covariance matrix H\n", "    H = np.dot(source_centered.T, target_centered)\n", "    \n", "    # Singular Value Decomposition\n", "    U, S, Vt = np.linalg.svd(H)\n", "    \n", "    # Compute rotation matrix R\n", "    R = np.dot(Vt.T, U.T)\n", "    \n", "    # Ensure proper rotation (det(R) = 1)\n", "    if np.linalg.det(R) < 0:\n", "        Vt[-1, :] *= -1\n", "        R = np.dot(Vt.T, U.T)\n", "    \n", "    # Compute translation\n", "    t = target_centroid - np.dot(R, source_centroid)\n", "    \n", "    # Create homogeneous transformation matrix\n", "    T = np.identity(4)\n", "    T[:3, :3] = R\n", "    T[:3, 3] = t\n", "    \n", "    return T, R, t"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["def icp_refinement(source, target, max_iterations=20, tolerance=1e-6, verbose=False):\n", "    \"\"\"\n", "    ICP algorithm for fine-tuning alignment after neural network coarse alignment.\n", "    \"\"\"\n", "    # Make a copy of the source point cloud\n", "    source_copy = np.copy(source)\n", "    prev_error = float('inf')\n", "    convergence_history = []\n", "    \n", "    # Initialize transformation matrix\n", "    T_icp = np.identity(4)\n", "    \n", "    start_time = time.time()\n", "    \n", "    for iteration in range(max_iterations):\n", "        # Find nearest neighbors\n", "        distances, indices = nearest_neighbor(source_copy, target)\n", "        \n", "        # Compute mean squared error\n", "        mean_error = np.mean(distances**2)\n", "        convergence_history.append(mean_error)\n", "        \n", "        # Check for convergence\n", "        if verbose:\n", "            logger.info(f\"ICP Iteration {iteration+1:3d}, MSE: {mean_error:.10f}\")\n", "        \n", "        if abs(prev_error - mean_error) < tolerance:\n", "            if verbose:\n", "                logger.info(f\"ICP converged after {iteration+1} iterations.\")\n", "            break\n", "        \n", "        prev_error = mean_error\n", "        \n", "        # Get corresponding points\n", "        corresponding_target_points = target[indices]\n", "        \n", "        # Compute transformation\n", "        T, R, t = best_fit_transform(source_copy, corresponding_target_points)\n", "        \n", "        # Update transformation matrix\n", "        T_icp = np.dot(T, T_icp)\n", "        \n", "        # Apply transformation\n", "        source_copy = np.dot(source_copy, R.T) + t\n", "    \n", "    end_time = time.time()\n", "    \n", "    if verbose:\n", "        logger.info(f\"ICP refinement completed in {end_time - start_time:.4f} seconds\")\n", "        if iteration == max_iterations - 1:\n", "            logger.info(f\"Warning: Maximum ICP iterations ({max_iterations}) reached without convergence.\")\n", "    \n", "    return T_icp, source_copy, mean_error, iteration + 1, convergence_history"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Main Hybrid Alignment Algorithm\n", "\n", "Implement the complete hybrid alignment workflow."]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["def hybrid_alignment(source_pc, target_pc, model=None, config=None):\n", "    \"\"\"\n", "    Complete hybrid alignment workflow: Neural Network + ICP.\n", "    \"\"\"\n", "    if config is None:\n", "        config = HybridAlignmentConfig()\n", "    \n", "    logger.info(\"=== Starting Hybrid Alignment (Neural Network + ICP) ===\")\n", "    logger.info(f\"Source points: {source_pc.shape[0]}\")\n", "    logger.info(f\"Target points: {target_pc.shape[0]}\")\n", "    \n", "    # Stage 1: Neural Network Coarse Alignment\n", "    logger.info(\"\\n--- Stage 1: Neural Network Coarse Alignment ---\")\n", "    aligned_source_neural, R_neural, t_neural, neural_time = neural_network_coarse_alignment(\n", "        source_pc, target_pc, model=model, num_points=config.NUM_POINTS\n", "    )\n", "    \n", "    # Optional: Downsample for ICP stage to improve performance\n", "    if config.USE_DOWNSAMPLING:\n", "        logger.info(f\"Downsampling point clouds for ICP refinement (voxel size: {config.ICP_VOXEL_SIZE})...\")\n", "        aligned_source_downsampled = downsample_voxel(aligned_source_neural, config.ICP_VOXEL_SIZE)\n", "        target_downsampled = downsample_voxel(target_pc, config.ICP_VOXEL_SIZE)\n", "        logger.info(f\"Downsampled source: {aligned_source_downsampled.shape[0]} points\")\n", "        logger.info(f\"Downsampled target: {target_downsampled.shape[0]} points\")\n", "        \n", "        icp_source = aligned_source_downsampled\n", "        icp_target = target_downsampled\n", "    else:\n", "        icp_source = aligned_source_neural\n", "        icp_target = target_pc\n", "    \n", "    # Stage 2: ICP Refinement\n", "    logger.info(\"--- Stage 2: ICP Refinement ---\")\n", "    icp_start_time = time.time()\n", "    T_icp, aligned_source_icp, icp_error, icp_iterations, icp_convergence = icp_refinement(\n", "        icp_source, icp_target, \n", "        max_iterations=config.ICP_MAX_ITERATIONS, \n", "        tolerance=config.ICP_TOLERANCE, \n", "        verbose=True\n", "    )\n", "    icp_time = time.time() - icp_start_time\n", "    \n", "    # Apply ICP transformation to the full resolution neural network result\n", "    if config.USE_DOWNSAMPLING:\n", "        # Extract rotation and translation from ICP transformation matrix\n", "        R_icp = T_icp[:3, :3]\n", "        t_icp = T_icp[:3, 3]\n", "        \n", "        # Apply to full resolution\n", "        aligned_source_final = apply_transformation(aligned_source_neural, R_icp, t_icp)\n", "    else:\n", "        aligned_source_final = aligned_source_icp\n", "    \n", "    # Combine transformations\n", "    T_neural = np.identity(4)\n", "    T_neural[:3, :3] = R_neural\n", "    T_neural[:3, 3] = t_neural\n", "    \n", "    T_combined = np.dot(T_icp, T_neural)\n", "    \n", "    # Calculate final metrics\n", "    tree = cKDTree(target_pc)\n", "    distances, _ = tree.query(aligned_source_final)\n", "    final_rmse = np.sqrt(np.mean(distances**2))\n", "    final_mean_distance = np.mean(distances)\n", "    \n", "    # Total time\n", "    total_time = neural_time + icp_time\n", "    \n", "    logger.info(f\"=== Hybrid Alignment Results ===\")\n", "    logger.info(f\"Neural network time: {neural_time:.4f} seconds\")\n", "    logger.info(f\"ICP refinement time: {icp_time:.4f} seconds\")\n", "    logger.info(f\"Total hybrid time: {total_time:.4f} seconds\")\n", "    logger.info(f\"ICP iterations: {icp_iterations}\")\n", "    logger.info(f\"Final RMSE: {final_rmse:.6f}\")\n", "    logger.info(f\"Final mean distance: {final_mean_distance:.6f}\")\n", "    \n", "    # Prepare results dictionary\n", "    results = {\n", "        'method': 'Hybrid (Neural Network + ICP)',\n", "        'aligned_points': aligned_source_final,\n", "        'transformation_matrix': T_combined,\n", "        'neural_network': {\n", "            'rotation_matrix': R_<PERSON>,\n", "            'translation_vector': t_neural,\n", "            'transformation_matrix': T_neural,\n", "            'time': neural_time\n", "        },\n", "        'icp_refinement': {\n", "            'transformation_matrix': T_icp,\n", "            'iterations': icp_iterations,\n", "            'final_error': icp_error,\n", "            'convergence_history': icp_convergence,\n", "            'time': icp_time\n", "        },\n", "        'performance_metrics': {\n", "            'rmse': final_rmse,\n", "            'mean_distance': final_mean_distance,\n", "            'neural_time': neural_time,\n", "            'icp_time': icp_time,\n", "            'total_time': total_time,\n", "            'icp_iterations': icp_iterations,\n", "            'converged': icp_iterations < config.ICP_MAX_ITERATIONS\n", "        },\n", "        'timing_info': {\n", "            'neural_network_time': neural_time,\n", "            'icp_refinement_time': icp_time,\n", "            'total_time': total_time\n", "        }\n", "    }\n", "    \n", "    return results"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Data Loading and Execution\n", "\n", "Load point cloud data and execute hybrid alignment."]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:__main__:Loading point cloud data for hybrid alignment...\n", "INFO:__main__:Point cloud files not found. Creating synthetic test data...\n", "INFO:__main__:Source points shape: (5000, 3)\n", "INFO:__main__:Target points shape: (5000, 3)\n"]}], "source": ["# Load source and target point clouds\n", "logger.info(\"Loading point cloud data for hybrid alignment...\")\n", "\n", "# Try to find point cloud files in the input directory\n", "point_cloud_files = list(config.input_path.glob('*.las')) + list(config.input_path.glob('*.pcd')) + list(config.input_path.glob('*.ply'))\n", "\n", "if len(point_cloud_files) >= 2:\n", "    # Load actual point clouds\n", "    source_file = point_cloud_files[0]\n", "    target_file = point_cloud_files[1]\n", "    \n", "    logger.info(f\"Loading source: {source_file}\")\n", "    # Use the read_point_cloud_file function from ICP notebook\n", "    if source_file.suffix.lower() == '.las':\n", "        las_data = laspy.read(source_file)\n", "        source_points = np.column_stack((las_data.x, las_data.y, las_data.z))\n", "    else:\n", "        pcd = o3d.io.read_point_cloud(str(source_file))\n", "        source_points = np.asarray(pcd.points)\n", "    \n", "    logger.info(f\"Loading target: {target_file}\")\n", "    if target_file.suffix.lower() == '.las':\n", "        las_data = laspy.read(target_file)\n", "        target_points = np.column_stack((las_data.x, las_data.y, las_data.z))\n", "    else:\n", "        pcd = o3d.io.read_point_cloud(str(target_file))\n", "        target_points = np.asarray(pcd.points)\n", "        \n", "else:\n", "    logger.info(\"Point cloud files not found. Creating synthetic test data...\")\n", "    # Create synthetic point clouds for testing\n", "    np.random.seed(42)\n", "    source_points = np.random.rand(5000, 3) * 2 - 1  # Random points in [-1, 1]\n", "    \n", "    # Create target by applying a known transformation\n", "    angle = np.radians(25)  # 25 degree rotation\n", "    R_true = np.array([\n", "        [np.cos(angle), -np.sin(angle), 0],\n", "        [np.sin(angle), np.cos(angle), 0],\n", "        [0, 0, 1]\n", "    ])\n", "    t_true = np.array([0.3, 0.2, 0.1])\n", "    target_points = np.dot(source_points, R_true.T) + t_true\n", "    \n", "    # Add some noise\n", "    target_points += np.random.normal(0, 0.01, target_points.shape)\n", "\n", "logger.info(f\"Source points shape: {source_points.shape}\")\n", "logger.info(f\"Target points shape: {target_points.shape}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Execute Hybrid Alignment\n", "\n", "Run the complete hybrid alignment workflow."]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:__main__:=== Starting Hybrid Alignment (Neural Network + ICP) ===\n", "INFO:__main__:Source points: 5000\n", "INFO:__main__:Target points: 5000\n", "INFO:__main__:\n", "--- Stage 1: Neural Network Coarse Alignment ---\n", "INFO:__main__:No trained model available. Using synthetic coarse alignment...\n", "INFO:__main__:Neural network coarse alignment completed in 0.0004 seconds\n", "INFO:__main__:Neural network rotation matrix:\n", "[[ 0.99022079 -0.13931227  0.00740845]\n", " [ 0.13928406  0.99024357  0.00419849]\n", " [-0.00792107 -0.00312555  0.99996374]]\n", "INFO:__main__:Neural network translation vector: [0.13594416 0.10007782 0.03027111]\n", "INFO:__main__:Downsampling point clouds for ICP refinement (voxel size: 0.02)...\n", "INFO:__main__:Downsampled source: 4991 points\n", "INFO:__main__:Downsampled target: 4989 points\n", "INFO:__main__:--- Stage 2: ICP Refinement ---\n", "INFO:__main__:ICP Iteration   1, MSE: 0.0097909146\n", "INFO:__main__:ICP Iteration   2, MSE: 0.0091572414\n", "INFO:__main__:ICP Iteration   3, MSE: 0.0086579344\n", "INFO:__main__:ICP Iteration   4, MSE: 0.0082340333\n", "INFO:__main__:ICP Iteration   5, MSE: 0.0078502176\n", "INFO:__main__:ICP Iteration   6, MSE: 0.0074918489\n", "INFO:__main__:ICP Iteration   7, MSE: 0.0071539301\n", "INFO:__main__:ICP Iteration   8, MSE: 0.0068462740\n", "INFO:__main__:ICP Iteration   9, MSE: 0.0065875817\n", "INFO:__main__:ICP Iteration  10, MSE: 0.0063573520\n", "INFO:__main__:ICP Iteration  11, MSE: 0.0061277617\n", "INFO:__main__:ICP Iteration  12, MSE: 0.0059014126\n", "INFO:__main__:ICP Iteration  13, MSE: 0.0056782230\n", "INFO:__main__:ICP Iteration  14, MSE: 0.0054567687\n", "INFO:__main__:ICP Iteration  15, MSE: 0.0052446837\n", "INFO:__main__:ICP Iteration  16, MSE: 0.0050181449\n", "INFO:__main__:ICP Iteration  17, MSE: 0.0047674546\n", "INFO:__main__:ICP Iteration  18, MSE: 0.0045014638\n", "INFO:__main__:ICP Iteration  19, MSE: 0.0042004389\n", "INFO:__main__:ICP Iteration  20, MSE: 0.0038432184\n", "INFO:__main__:ICP refinement completed in 0.1109 seconds\n", "INFO:__main__:Warning: Maximum ICP iterations (20) reached without convergence.\n", "INFO:__main__:=== Hybrid Alignment Results ===\n", "INFO:__main__:Neural network time: 0.0004 seconds\n", "INFO:__main__:ICP refinement time: 0.1118 seconds\n", "INFO:__main__:Total hybrid time: 0.1122 seconds\n", "INFO:__main__:ICP iterations: 20\n", "INFO:__main__:Final RMSE: 0.057840\n", "INFO:__main__:Final mean distance: 0.053082\n", "INFO:__main__:\n", "Hybrid alignment completed successfully!\n"]}], "source": ["# Execute hybrid alignment\n", "hybrid_results = hybrid_alignment(\n", "    source_points, \n", "    target_points, \n", "    model=None,  # No trained model available, will use synthetic neural network\n", "    config=config\n", ")\n", "\n", "logger.info(\"\\nHybrid alignment completed successfully!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. Results Analysis and Visualization\n", "\n", "Analyze and visualize the hybrid alignment results."]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:__main__:=== Detailed Hybrid Alignment Analysis ===\n", "INFO:__main__:Method: Hybrid (Neural Network + ICP)\n", "INFO:__main__:\n", "Performance Metrics:\n", "INFO:__main__:  rmse: 0.057840\n", "INFO:__main__:  mean_distance: 0.053082\n", "INFO:__main__:  neural_time: 0.000435\n", "INFO:__main__:  icp_time: 0.111812\n", "INFO:__main__:  total_time: 0.112247\n", "INFO:__main__:  icp_iterations: 20\n", "INFO:__main__:  converged: False\n", "INFO:__main__:Timing Breakdown:\n", "INFO:__main__:  neural_network_time: 0.0004 seconds\n", "INFO:__main__:  icp_refinement_time: 0.1118 seconds\n", "INFO:__main__:  total_time: 0.1122 seconds\n", "INFO:__main__:Neural Network Stage:\n", "INFO:__main__:  Translation: [0.13594416 0.10007782 0.03027111]\n", "INFO:__main__:  Time: 0.0004 seconds\n", "INFO:__main__:ICP Refinement Stage:\n", "INFO:__main__:  Iterations: 20\n", "INFO:__main__:  Final Error: 0.0038432184\n", "INFO:__main__:  Time: 0.1118 seconds\n"]}], "source": ["# Display detailed results\n", "logger.info(\"=== Detailed Hybrid Alignment Analysis ===\")\n", "logger.info(f\"Method: {hybrid_results['method']}\")\n", "logger.info(f\"\\nPerformance Metrics:\")\n", "for metric, value in hybrid_results['performance_metrics'].items():\n", "    if isinstance(value, float):\n", "        logger.info(f\"  {metric}: {value:.6f}\")\n", "    else:\n", "        logger.info(f\"  {metric}: {value}\")\n", "\n", "logger.info(f\"Timing Breakdown:\")\n", "for stage, time_val in hybrid_results['timing_info'].items():\n", "    logger.info(f\"  {stage}: {time_val:.4f} seconds\")\n", "\n", "logger.info(f\"Neural Network Stage:\")\n", "logger.info(f\"  Translation: {hybrid_results['neural_network']['translation_vector']}\")\n", "logger.info(f\"  Time: {hybrid_results['neural_network']['time']:.4f} seconds\")\n", "\n", "logger.info(f\"ICP Refinement Stage:\")\n", "logger.info(f\"  Iterations: {hybrid_results['icp_refinement']['iterations']}\")\n", "logger.info(f\"  Final Error: {hybrid_results['icp_refinement']['final_error']:.10f}\")\n", "logger.info(f\"  Time: {hybrid_results['icp_refinement']['time']:.4f} seconds\")"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:__main__:Saved aligned point cloud to:\n", "INFO:__main__:  PCD: ../../data/ENEL/Trino/alignment/hybrid_results/hybrid_aligned_source.pcd\n", "INFO:__main__:  PLY: ../../data/ENEL/Trino/alignment/hybrid_results/hybrid_aligned_source.ply\n", "INFO:__main__:Saved metadata to: ../../data/ENEL/Trino/alignment/hybrid_results/hybrid_alignment_metadata.json\n", "INFO:__main__:Hybrid alignment workflow completed successfully!\n"]}], "source": ["# Save aligned point cloud and metadata\n", "aligned_pcd = o3d.geometry.PointCloud()\n", "aligned_pcd.points = o3d.utility.Vector3dVector(hybrid_results['aligned_points'])\n", "\n", "# Save in multiple formats\n", "output_file_pcd = config.hybrid_results_path / 'hybrid_aligned_source.pcd'\n", "output_file_ply = config.hybrid_results_path / 'hybrid_aligned_source.ply'\n", "\n", "o3d.io.write_point_cloud(str(output_file_pcd), aligned_pcd)\n", "o3d.io.write_point_cloud(str(output_file_ply), aligned_pcd)\n", "\n", "logger.info(f\"Saved aligned point cloud to:\")\n", "logger.info(f\"  PCD: {output_file_pcd}\")\n", "logger.info(f\"  PLY: {output_file_ply}\")\n", "\n", "# Save metadata\n", "metadata = {\n", "    'method': hybrid_results['method'],\n", "    'timestamp': datetime.now().isoformat(),\n", "    'performance_metrics': hybrid_results['performance_metrics'],\n", "    'timing_info': hybrid_results['timing_info'],\n", "    'transformation_matrix': hybrid_results['transformation_matrix'].tolist()\n", "}\n", "\n", "metadata_file = config.hybrid_results_path / 'hybrid_alignment_metadata.json'\n", "with open(metadata_file, 'w') as f:\n", "    json.dump(metadata, f, indent=2)\n", "\n", "logger.info(f\"Saved metadata to: {metadata_file}\")\n", "logger.info(\"Hybrid alignment workflow completed successfully!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Results Export\n", "\n", "Export alignment results in standardized format for analysis."]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:__main__:Results exported:\n", "INFO:__main__:  Individual: ../../data/output_runs/alignment_testing/csf/results/hybrid_csf_results.json\n", "INFO:__main__:  Master: ../../data/output_runs/alignment_testing/master_results.csv\n", "INFO:__main__:=== Hybrid Test Completed ===\n", "INFO:__main__:Ground Method: csf\n", "INFO:__main__:Alignment Method: hybrid\n", "INFO:__main__:Success: True\n", "INFO:__main__:Final RMSE: 0.05784037035259582\n", "INFO:__main__:Total Time: 0.11224699020385742s\n", "INFO:__main__:Neural Network Time: 0.00043487548828125s\n", "INFO:__main__:ICP Refinement Time: 0.11181211471557617s\n"]}], "source": ["# Results Export Cell - Add at the end of each alignment notebook\n", "import json\n", "import pandas as pd\n", "from datetime import datetime\n", "from pathlib import Path\n", "\n", "def export_alignment_results(ground_method, alignment_method, results_dict, output_dir):\n", "    \"\"\"Export alignment results in standardized format\"\"\"\n", "    \n", "    # Prepare results data\n", "    results_data = {\n", "        'ground_method': ground_method,\n", "        'alignment_method': alignment_method,\n", "        'site_name': site_name,\n", "        'timestamp': datetime.now().isoformat(),\n", "        'source_file': str(source_file),\n", "        'target_file': str(target_file),  # Convert to string for JSON serialization\n", "        'mlflow_run_name': mlflow_run_name,\n", "        **results_dict  # Include method-specific results\n", "    }\n", "    \n", "    # Create results directory\n", "    results_dir = Path(output_dir) / \"results\"\n", "    results_dir.mkdir(parents=True, exist_ok=True)\n", "    \n", "    # Save individual result file\n", "    result_file = results_dir / f\"{alignment_method}_{ground_method}_results.json\"\n", "    with open(result_file, 'w') as f:\n", "        json.dump(results_data, f, indent=2)\n", "    \n", "    # Append to master results file\n", "    master_file = Path(output_dir).parent / \"master_results.csv\"\n", "    \n", "    # Convert to DataFrame row\n", "    df_row = pd.DataFrame([results_data])\n", "    \n", "    # Append to master file\n", "    if master_file.exists():\n", "        df_existing = pd.read_csv(master_file)\n", "        df_combined = pd.concat([df_existing, df_row], ignore_index=True)\n", "    else:\n", "        df_combined = df_row\n", "    \n", "    df_combined.to_csv(master_file, index=False)\n", "    \n", "    logger.info(f\"Results exported:\")\n", "    logger.info(f\"  Individual: {result_file}\")\n", "    logger.info(f\"  Master: {master_file}\")\n", "    \n", "    return results_data\n", "\n", "# Use actual Hybrid results from execution\n", "# NOTE: Update these variables with actual results from your hybrid execution\n", "try:\n", "    # Try to use actual results if they exist\n", "    alignment_results = {\n", "        'success': True,  # UPDATE: True/False based on execution completion\n", "        'final_rmse': hybrid_results['performance_metrics']['rmse'] if 'hybrid_results' in locals() else 0.0,  # Final RMSE\n", "        'neural_time': hybrid_results['timing_info']['neural_network_time'] if 'hybrid_results' in locals() else 0.0,  # Neural network time\n", "        'icp_time': hybrid_results['timing_info']['icp_refinement_time'] if 'hybrid_results' in locals() else 0.0,  # ICP time\n", "        'total_time': hybrid_results['timing_info']['total_time'] if 'hybrid_results' in locals() else 0.0,  # Total time\n", "        'icp_iterations': hybrid_results['icp_refinement']['iterations'] if 'hybrid_results' in locals() else 0,  # ICP iterations\n", "        'converged': hybrid_results['performance_metrics']['converged'] if 'hybrid_results' in locals() else False,  # Convergence status\n", "        'point_count_source': len(source_points) if 'source_points' in locals() else 0,  # Source point count\n", "        'point_count_target': len(target_points) if 'target_points' in locals() else 0,  # Target point count\n", "        'transformation_matrix': hybrid_results['transformation_matrix'].tolist() if 'hybrid_results' in locals() else [],  # Final transformation\n", "        'notes': f'Hybrid alignment (Neural Network + ICP) with {ground_method} ground segmentation'  # Method-specific notes\n", "    }\n", "except (<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>):\n", "    # Fallback if variables don't exist\n", "    alignment_results = {\n", "        'success': <PERSON><PERSON><PERSON>,\n", "        'final_rmse': 999.0,\n", "        'neural_time': 0.0,\n", "        'icp_time': 0.0,\n", "        'total_time': 0.0,\n", "        'icp_iterations': 0,\n", "        'converged': <PERSON><PERSON><PERSON>,\n", "        'point_count_source': 0,\n", "        'point_count_target': 0,\n", "        'transformation_matrix': [],\n", "        'notes': f'Hybrid alignment failed - variables not found'\n", "    }\n", "\n", "# Extract alignment method from mlflow_run_name\n", "alignment_method = \"hybrid\"\n", "\n", "# Export results\n", "exported_results = export_alignment_results(\n", "    ground_method=ground_method,\n", "    alignment_method=alignment_method,\n", "    results_dict=alignment_results,\n", "    output_dir=output_dir\n", ")\n", "\n", "logger.info(f\"=== Hybrid Test Completed ===\")\n", "logger.info(f\"Ground Method: {ground_method}\")\n", "logger.info(f\"Alignment Method: {alignment_method}\")\n", "logger.info(f\"Success: {alignment_results['success']}\")\n", "logger.info(f\"Final RMSE: {alignment_results['final_rmse']}\")\n", "logger.info(f\"Total Time: {alignment_results['total_time']}s\")\n", "logger.info(f\"Neural Network Time: {alignment_results['neural_time']}s\")\n", "logger.info(f\"ICP Refinement Time: {alignment_results['icp_time']}s\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Research Summary\n", "\n", "### Hybrid Alignment Method Analysis\n", "\n", "| Aspect | Details |\n", "|--------|--------|\n", "| **Algorithm** | Neural Network + ICP Refinement (Hybrid Approach) |\n", "| **Dataset** | Non-ground points from ground segmentation + IFC point cloud |\n", "| **Key Attributes** | Multi-modal features, learned + geometric optimization |\n", "| **Performance** | TBD - Combines strengths of both approaches |\n", "| **Advantages** | • Best of both worlds<br/>• Neural network initialization + ICP refinement<br/>• Robust and accurate |\n", "| **Research Gaps** | • Parameter balancing<br/>• Computational complexity<br/>• Validation methodology |\n", "\n", "### Hybrid Strategy:\n", "1. **Phase 1**: Neural network provides initial coarse alignment\n", "2. **Phase 2**: ICP refinement for precise registration\n", "3. **Weight Balancing**: Neural network weight (0.6) + ICP weight (0.4)\n", "4. **Iterative Refinement**: Multiple rounds of hybrid optimization\n", "\n", "### Expected Benefits:\n", "- Overcome ICP local minima issues with neural network initialization\n", "- Achieve high precision through ICP refinement\n", "- Robust performance across different point cloud qualities\n", "- Adaptive to various infrastructure types\n", "\n", "### Implementation Challenges:\n", "- Optimal weight balancing between methods\n", "- Computational efficiency optimization\n", "- Training data requirements for neural component"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}