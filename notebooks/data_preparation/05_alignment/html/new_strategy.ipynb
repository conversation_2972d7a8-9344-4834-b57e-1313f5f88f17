{"cells": [{"cell_type": "code", "execution_count": null, "id": "3bca5de6", "metadata": {}, "outputs": [], "source": ["# STEP 1: Add this to your existing code right after loading ifc_points\n", "# This will tell you what type of data you're working with\n", "\n", "def quick_ifc_diagnosis(ifc_points):\n", "    \"\"\"Quick diagnosis of IFC data type\"\"\"\n", "    \n", "    print(\"=== QUICK IFC DATA DIAGNOSIS ===\")\n", "    \n", "    # Basic stats\n", "    n_points = len(ifc_points)\n", "    x_range = ifc_points[:, 0].max() - ifc_points[:, 0].min()\n", "    y_range = ifc_points[:, 1].max() - ifc_points[:, 1].min()\n", "    z_range = ifc_points[:, 2].max() - ifc_points[:, 2].min()\n", "    z_std = np.std(ifc_points[:, 2])\n", "    \n", "    print(f\"Point count: {n_points:,}\")\n", "    print(f\"Spatial extent: {x_range:.1f} x {y_range:.1f} x {z_range:.1f} m\")\n", "    print(f\"Z standard deviation: {z_std:.2f} m\")\n", "    \n", "    # Grid pattern check (for piles)\n", "    if n_points < 10000:  # Only check if reasonable size\n", "        xy_points = ifc_points[:, :2]\n", "        tree = cKDTree(xy_points)\n", "        distances, _ = tree.query(xy_points, k=2)\n", "        nn_distances = distances[:, 1]\n", "        mean_spacing = np.mean(nn_distances)\n", "        cv = np.std(nn_distances) / mean_spacing if mean_spacing > 0 else float('inf')\n", "        \n", "        print(f\"Nearest neighbor spacing: {mean_spacing:.2f} ± {np.std(nn_distances):.2f} m\")\n", "        print(f\"Spacing regularity (CV): {cv:.3f}\")\n", "        \n", "        is_regular_grid = cv < 0.3\n", "        print(f\"Regular grid pattern: {is_regular_grid}\")\n", "    else:\n", "        is_regular_grid = False\n", "        mean_spacing = \"N/A (too many points)\"\n", "    \n", "    # Data type determination\n", "    if n_points < 1000 and z_range < 5 and is_regular_grid:\n", "        data_type = \"PILES_ONLY\"\n", "        recommendation = \"Use all pile points, no downsampling. Focus on XY grid alignment.\"\n", "    elif n_points < 5000 and z_range < 10:\n", "        data_type = \"FOUNDATION_ELEMENTS\" \n", "        recommendation = \"Focus on foundation corners and edges. Use moderate downsampling.\"\n", "    elif z_range > 20:\n", "        data_type = \"FULL_BUILDING\"\n", "        recommendation = \"Use feature-based alignment on architectural elements.\"\n", "    else:\n", "        data_type = \"MIXED_STRUCTURAL\"\n", "        recommendation = \"Use hybrid approach with multiple feature types.\"\n", "    \n", "    print(f\"\\n>>> DIAGNOSIS: {data_type}\")\n", "    print(f\">>> RECOMMENDATION: {recommendation}\")\n", "    \n", "    return data_type, {\n", "        'n_points': n_points,\n", "        'z_range': z_range,\n", "        'is_regular_grid': is_regular_grid,\n", "        'mean_spacing': mean_spacing\n", "    }\n", "\n", "# Run this diagnosis\n", "ifc_data_type, ifc_stats = quick_ifc_diagnosis(ifc_points_pointcloud)"]}, {"cell_type": "code", "execution_count": null, "id": "e9254e1b", "metadata": {}, "outputs": [], "source": ["# OPTIMIZED APPROACH FOR YOUR SPECIFIC DATA\n", "# 5.48M points, 1.45km x 1.56km area, mixed structural elements\n", "\n", "ifc_points = ifc_points_pointcloud\n", "\n", "def large_scale_mixed_alignment(drone_points, ifc_points):\n", "    \"\"\"Specialized alignment for large-scale mixed structural data\"\"\"\n", "    \n", "    print(\"=== LARGE-SCALE MIXED STRUCTURAL ALIGNMENT ===\")\n", "    print(f\"IFC points: {len(ifc_points):,}\")\n", "    print(f\"Drone points: {len(drone_points):,}\")\n", "    \n", "    # STEP 1: Intelligent hierarchical sampling\n", "    def hierarchical_sampling(points, target_size=20000):\n", "        \"\"\"Multi-level sampling preserving spatial distribution\"\"\"\n", "        \n", "        if len(points) <= target_size:\n", "            return points\n", "        \n", "        print(f\"Hierarchical sampling from {len(points):,} to {target_size:,} points...\")\n", "        \n", "        # Level 1: Spatial grid sampling (preserve spatial distribution)\n", "        grid_size = 10.0  # 10m grid for large site\n", "        min_coords = np.min(points, axis=0)\n", "        max_coords = np.max(points, axis=0)\n", "        \n", "        x_bins = np.arange(min_coords[0], max_coords[0] + grid_size, grid_size)\n", "        y_bins = np.arange(min_coords[1], max_coords[1] + grid_size, grid_size)\n", "        \n", "        grid_samples = []\n", "        points_per_cell = target_size // (len(x_bins) * len(y_bins))\n", "        points_per_cell = max(1, points_per_cell)\n", "        \n", "        for i in range(len(x_bins) - 1):\n", "            for j in range(len(y_bins) - 1):\n", "                mask = ((points[:, 0] >= x_bins[i]) & (points[:, 0] < x_bins[i+1]) &\n", "                       (points[:, 1] >= y_bins[j]) & (points[:, 1] < y_bins[j+1]))\n", "                \n", "                cell_points = points[mask]\n", "                if len(cell_points) > 0:\n", "                    n_sample = min(points_per_cell, len(cell_points))\n", "                    if n_sample > 0:\n", "                        indices = np.random.choice(len(cell_points), n_sample, replace=False)\n", "                        grid_samples.append(cell_points[indices])\n", "        \n", "        if grid_samples:\n", "            sampled_points = np.vstack(grid_samples)\n", "            # If we have too many, randomly subsample to exact target\n", "            if len(sampled_points) > target_size:\n", "                indices = np.random.choice(len(sampled_points), target_size, replace=False)\n", "                sampled_points = sampled_points[indices]\n", "            return sampled_points\n", "        else:\n", "            # Fallback to random sampling\n", "            indices = np.random.choice(len(points), target_size, replace=False)\n", "            return points[indices]\n", "    \n", "    # STEP 2: Separate into pile and structural components\n", "    def separate_components(ifc_points):\n", "        \"\"\"Separate piles from other structural elements\"\"\"\n", "        \n", "        print(\"Analyzing IFC components...\")\n", "        \n", "        # Use DBSCAN clustering to identify distinct components\n", "        # Sample for clustering analysis\n", "        cluster_sample_size = min(50000, len(ifc_points))\n", "        cluster_indices = np.random.choice(len(ifc_points), cluster_sample_size, replace=False)\n", "        cluster_sample = ifc_points[cluster_indices]\n", "        \n", "        # Cluster in XY plane to identify building footprint vs isolated elements\n", "        from sklearn.cluster import DBSCAN\n", "        xy_points = cluster_sample[:, :2]\n", "        \n", "        # Use adaptive eps based on site size\n", "        site_diagonal = np.sqrt(1452.8**2 + 1561.7**2)\n", "        eps = site_diagonal * 0.001  # 0.1% of site diagonal\n", "        \n", "        clustering = DBSCAN(eps=eps, min_samples=10).fit(xy_points)\n", "        \n", "        # Analyze clusters\n", "        unique_labels = set(clustering.labels_)\n", "        cluster_info = {}\n", "        \n", "        for label in unique_labels:\n", "            if label == -1:  # Noise points\n", "                continue\n", "            \n", "            cluster_mask = clustering.labels_ == label\n", "            cluster_points = cluster_sample[cluster_mask]\n", "            \n", "            cluster_info[label] = {\n", "                'size': len(cluster_points),\n", "                'z_range': np.max(cluster_points[:, 2]) - np.min(cluster_points[:, 2]),\n", "                'area': (np.max(cluster_points[:, 0]) - np.min(cluster_points[:, 0])) * \n", "                       (np.max(cluster_points[:, 1]) - np.min(cluster_points[:, 1]))\n", "            }\n", "        \n", "        # Identify likely pile clusters (small area, low height variation)\n", "        pile_clusters = []\n", "        building_clusters = []\n", "        \n", "        for label, info in cluster_info.items():\n", "            if info['area'] < 100 and info['z_range'] < 3:  # Small, low elements = likely piles\n", "                pile_clusters.append(label)\n", "            else:\n", "                building_clusters.append(label)\n", "        \n", "        print(f\"Identified {len(pile_clusters)} pile-like clusters, {len(building_clusters)} building clusters\")\n", "        \n", "        return pile_clusters, building_clusters, clustering\n", "    \n", "    # STEP 3: Multi-scale alignment approach\n", "    \n", "    # First, hierarchical sampling of both datasets\n", "    print(\"\\nStep 1: Hierarchical sampling...\")\n", "    drone_sampled = hierarchical_sampling(drone_points, 25000)\n", "    ifc_sampled = hierarchical_sampling(ifc_points, 25000)\n", "    \n", "    print(f\"Sampled - Drone: {len(drone_sampled):,}, IFC: {len(ifc_sampled):,}\")\n", "    \n", "    # Step 2: Initial coarse alignment using centroids\n", "    print(\"\\nStep 2: Initial coarse alignment...\")\n", "    drone_center = np.mean(drone_sampled, axis=0)\n", "    ifc_center = np.mean(ifc_sampled, axis=0)\n", "    initial_offset = ifc_center - drone_center\n", "    \n", "    print(f\"Initial centroid offset: [{initial_offset[0]:.2f}, {initial_offset[1]:.2f}, {initial_offset[2]:.2f}]\")\n", "    \n", "    # Apply initial alignment\n", "    drone_aligned = drone_sampled + initial_offset\n", "    \n", "    # Step 3: Create point clouds with outlier removal\n", "    print(\"\\nStep 3: Creating cleaned point clouds...\")\n", "    \n", "    source_pcd = o3d.geometry.PointCloud()\n", "    source_pcd.points = o3d.utility.Vector3dVector(drone_aligned)\n", "    \n", "    target_pcd = o3d.geometry.PointCloud()\n", "    target_pcd.points = o3d.utility.Vector3dVector(ifc_sampled)\n", "    \n", "    # <PERSON><PERSON><PERSON> downsampling for performance\n", "    voxel_size = 0.5  # 50cm voxels for large site\n", "    source_pcd = source_pcd.voxel_down_sample(voxel_size)\n", "    target_pcd = target_pcd.voxel_down_sample(voxel_size)\n", "    \n", "    print(f\"After voxel downsampling - Source: {len(source_pcd.points)}, Target: {len(target_pcd.points)}\")\n", "    \n", "    # Remove statistical outliers\n", "    source_pcd, _ = source_pcd.remove_statistical_outlier(nb_neighbors=20, std_ratio=2.0)\n", "    target_pcd, _ = target_pcd.remove_statistical_outlier(nb_neighbors=20, std_ratio=2.0)\n", "    \n", "    print(f\"After outlier removal - Source: {len(source_pcd.points)}, Target: {len(target_pcd.points)}\")\n", "    \n", "    # Estimate normals for point-to-plane ICP\n", "    source_pcd.estimate_normals(search_param=o3d.geometry.KDTreeSearchParamHybrid(radius=2.0, max_nn=30))\n", "    target_pcd.estimate_normals(search_param=o3d.geometry.KDTreeSearchParamHybrid(radius=2.0, max_nn=30))\n", "    \n", "    # Step 4: Multi-scale ICP alignment\n", "    print(\"\\nStep 4: Multi-scale ICP alignment...\")\n", "    \n", "    # Coarse ICP (large tolerance for initial alignment)\n", "    print(\"Running coarse ICP...\")\n", "    coarse_result = o3d.pipelines.registration.registration_icp(\n", "        source_pcd, target_pcd,\n", "        max_correspondence_distance=10.0,  # Large tolerance for big site\n", "        init=np.eye(4),\n", "        estimation_method=o3d.pipelines.registration.TransformationEstimationPointToPoint(),\n", "        criteria=o3d.pipelines.registration.ICPConvergenceCriteria(max_iteration=30)\n", "    )\n", "    \n", "    print(f\"Coarse ICP - Fitness: {coarse_result.fitness:.4f}, RMSE: {coarse_result.inlier_rmse:.2f}m\")\n", "    \n", "    # Medium ICP (moderate tolerance)\n", "    print(\"Running medium ICP...\")\n", "    medium_result = o3d.pipelines.registration.registration_icp(\n", "        source_pcd, target_pcd,\n", "        max_correspondence_distance=5.0,\n", "        init=coarse_result.transformation,\n", "        estimation_method=o3d.pipelines.registration.TransformationEstimationPointToPlane(),\n", "        criteria=o3d.pipelines.registration.ICPConvergenceCriteria(max_iteration=50)\n", "    )\n", "    \n", "    print(f\"Medium ICP - Fitness: {medium_result.fitness:.4f}, RMSE: {medium_result.inlier_rmse:.2f}m\")\n", "    \n", "    # Fine ICP (tight tolerance)\n", "    print(\"Running fine ICP...\")\n", "    fine_result = o3d.pipelines.registration.registration_icp(\n", "        source_pcd, target_pcd,\n", "        max_correspondence_distance=2.0,\n", "        init=medium_result.transformation,\n", "        estimation_method=o3d.pipelines.registration.TransformationEstimationPointToPlane(),\n", "        criteria=o3d.pipelines.registration.ICPConvergenceCriteria(max_iteration=100)\n", "    )\n", "    \n", "    print(f\"Fine ICP - Fitness: {fine_result.fitness:.4f}, RMSE: {fine_result.inlier_rmse:.2f}m\")\n", "    \n", "    # Combine initial offset with ICP transformation\n", "    final_transformation = fine_result.transformation.copy()\n", "    final_transformation[:3, 3] += initial_offset\n", "    \n", "    return final_transformation, fine_result, {\n", "        'initial_offset': initial_offset,\n", "        'coarse_result': coarse_result,\n", "        'medium_result': medium_result,\n", "        'fine_result': fine_result,\n", "        'drone_sampled_size': len(drone_sampled),\n", "        'ifc_sampled_size': len(ifc_sampled),\n", "        'final_source_size': len(source_pcd.points),\n", "        'final_target_size': len(target_pcd.points)\n", "    }\n", "\n", "# STEP 5: Run the optimized alignment\n", "print(\"Starting optimized alignment for large-scale mixed structural data...\")\n", "transformation, icp_result, alignment_info = large_scale_mixed_alignment(drone_points, ifc_points)\n", "\n", "# STEP 6: Comprehensive evaluation with regional analysis\n", "def regional_evaluation(drone_points, ifc_points, transformation):\n", "    \"\"\"Evaluate alignment quality across different regions of the site\"\"\"\n", "    \n", "    print(\"\\n=== REGIONAL ALIGNMENT EVALUATION ===\")\n", "    \n", "    # Apply transformation to all drone points\n", "    drone_transformed = np.dot(drone_points, transformation[:3, :3].T) + transformation[:3, 3]\n", "    \n", "    # Overall evaluation with large sample\n", "    np.random.seed(42)\n", "    n_sample = min(50000, len(drone_transformed))  # Larger sample for big dataset\n", "    sample_indices = np.random.choice(len(drone_transformed), n_sample, replace=False)\n", "    \n", "    drone_sample = drone_transformed[sample_indices]\n", "    ifc_sample = ifc_points[np.random.choice(len(ifc_points), \n", "                                           min(50000, len(ifc_points)), \n", "                                           replace=False)]\n", "    \n", "    tree = cKDTree(ifc_sample)\n", "    distances, _ = tree.query(drone_sample)\n", "    \n", "    overall_results = {\n", "        'rmse': np.sqrt(np.mean(distances**2)),\n", "        'median_distance': np.median(distances),\n", "        'mean_distance': np.mean(distances),\n", "        'good_points_1m': np.sum(distances < 1.0) / len(distances) * 100,\n", "        'good_points_2m': np.sum(distances < 2.0) / len(distances) * 100,\n", "        'good_points_5m': np.sum(distances < 5.0) / len(distances) * 100,\n", "        'good_points_10m': np.sum(distances < 10.0) / len(distances) * 100\n", "    }\n", "    \n", "    print(f\"OVERALL RESULTS (sample of {n_sample:,} points):\")\n", "    print(f\"  RMSE: {overall_results['rmse']:.2f}m\")\n", "    print(f\"  Median distance: {overall_results['median_distance']:.2f}m\")\n", "    print(f\"  Good points (<2m): {overall_results['good_points_2m']:.1f}%\")\n", "    print(f\"  Good points (<5m): {overall_results['good_points_5m']:.1f}%\")\n", "    print(f\"  Good points (<10m): {overall_results['good_points_10m']:.1f}%\")\n", "    \n", "    # Regional analysis (divide site into 4 quadrants)\n", "    print(f\"\\nREGIONAL ANALYSIS:\")\n", "    \n", "    drone_bounds = {\n", "        'x_min': np.min(drone_transformed[:, 0]),\n", "        'x_max': np.max(drone_transformed[:, 0]),\n", "        'y_min': np.min(drone_transformed[:, 1]),\n", "        'y_max': np.max(drone_transformed[:, 1])\n", "    }\n", "    \n", "    x_mid = (drone_bounds['x_min'] + drone_bounds['x_max']) / 2\n", "    y_mid = (drone_bounds['y_min'] + drone_bounds['y_max']) / 2\n", "    \n", "    quadrants = {\n", "        'NE': (drone_transformed[:, 0] >= x_mid) & (drone_transformed[:, 1] >= y_mid),\n", "        'NW': (drone_transformed[:, 0] < x_mid) & (drone_transformed[:, 1] >= y_mid),\n", "        'SE': (drone_transformed[:, 0] >= x_mid) & (drone_transformed[:, 1] < y_mid),\n", "        'SW': (drone_transformed[:, 0] < x_mid) & (drone_transformed[:, 1] < y_mid)\n", "    }\n", "    \n", "    regional_results = {}\n", "    for quad_name, quad_mask in quadrants.items():\n", "        quad_points = drone_transformed[quad_mask]\n", "        if len(quad_points) > 1000:  # Enough points for meaningful analysis\n", "            quad_sample_size = min(5000, len(quad_points))\n", "            quad_sample = quad_points[np.random.choice(len(quad_points), quad_sample_size, replace=False)]\n", "            \n", "            quad_distances, _ = tree.query(quad_sample)\n", "            quad_rmse = np.sqrt(np.mean(quad_distances**2))\n", "            quad_good_2m = np.sum(quad_distances < 2.0) / len(quad_distances) * 100\n", "            \n", "            regional_results[quad_name] = {\n", "                'rmse': quad_rmse,\n", "                'good_2m': quad_good_2m,\n", "                'n_points': len(quad_points)\n", "            }\n", "            \n", "            print(f\"  {quad_name}: RMSE {quad_rmse:.2f}m, Good(<2m) {quad_good_2m:.1f}%, Points {len(quad_points):,}\")\n", "    \n", "    return overall_results, regional_results, drone_transformed\n", "\n", "# Run comprehensive evaluation\n", "overall_eval, regional_eval, drone_final = regional_evaluation(drone_points, ifc_points, transformation)\n", "\n", "# STEP 7: Comparison and final assessment\n", "print(f\"\\n{'='*80}\")\n", "print(\"FINAL COMPARISON AND ASSESSMENT\")\n", "print(f\"{'='*80}\")\n", "\n", "improvement_factor = 28.92 / overall_eval['rmse']\n", "print(f\"Original hybrid method RMSE: 28.92m\")\n", "print(f\"Optimized method RMSE: {overall_eval['rmse']:.2f}m\")\n", "print(f\"Improvement: {28.92 - overall_eval['rmse']:.2f}m ({improvement_factor:.1f}x better)\")\n", "\n", "if overall_eval['rmse'] < 10.0:\n", "    assessment = \"EXCELLENT - Good alignment achieved\"\n", "elif overall_eval['rmse'] < 15.0:\n", "    assessment = \"GOOD - Reasonable alignment for large site\"\n", "elif overall_eval['rmse'] < 20.0:\n", "    assessment = \"MODERATE - Some improvement but needs refinement\"\n", "else:\n", "    assessment = \"POOR - Fundamental alignment issues remain\"\n", "\n", "print(f\"Assessment: {assessment}\")\n", "\n", "# Save results if significant improvement\n", "if overall_eval['rmse'] < 20.0:  # If reasonable improvement\n", "    print(f\"\\nSaving optimized results...\")\n", "    \n", "    # Save transformation matrix for future use\n", "    optimized_results = {\n", "        'method': 'large_scale_mixed_structural',\n", "        'ifc_data_stats': {\n", "            'total_points': len(ifc_points),\n", "            'spatial_extent': '1452.8 x 1561.7 x 8.8 m',\n", "            'data_type': 'MIXED_STRUCTURAL'\n", "        },\n", "        'transformation_matrix': transformation.tolist(),\n", "        'alignment_info': {\n", "            'initial_offset': alignment_info['initial_offset'].tolist(),\n", "            'icp_stages': {\n", "                'coarse_fitness': alignment_info['coarse_result'].fitness,\n", "                'medium_fitness': alignment_info['medium_result'].fitness,\n", "                'fine_fitness': alignment_info['fine_result'].fitness\n", "            }\n", "        },\n", "        'evaluation_results': overall_eval,\n", "        'regional_results': regional_eval,\n", "        'comparison': {\n", "            'original_rmse': 28.92,\n", "            'optimized_rmse': overall_eval['rmse'],\n", "            'improvement_factor': improvement_factor\n", "        }\n", "    }\n", "    \n", "    import json\n", "    with open(output_path / f\"{site_name}_large_scale_optimized_results.json\", 'w') as f:\n", "        json.dump(optimized_results, f, indent=2)\n", "    \n", "    print(f\"Results saved to: {output_path}\")\n", "\n", "print(f\"\\n{'='*80}\")\n", "print(\"NEXT STEPS RECOMMENDATION\")\n", "print(f\"{'='*80}\")\n", "\n", "if overall_eval['rmse'] < 5.0:\n", "    print(\"✓ Excellent alignment achieved!\")\n", "    print(\"  • Proceed with confidence\")\n", "    print(\"  • Consider fine-tuning with manual control points if needed\")\n", "elif overall_eval['rmse'] < 10.0:\n", "    print(\"✓ Good alignment for large-scale data\")\n", "    print(\"  • Acceptable for most analysis purposes\")\n", "    print(\"  • Monitor regional variations\")\n", "elif overall_eval['rmse'] < 15.0:\n", "    print(\"⚠ Moderate improvement achieved\")\n", "    print(\"  • Consider manual control point refinement\")\n", "    print(\"  • Check for systematic coordinate issues\")\n", "else:\n", "    print(\"⚠ Significant challenges remain\")\n", "    print(\"  • Verify coordinate system consistency\")\n", "    print(\"  • Check scale and units\")\n", "    print(\"  • Consider manual correspondence identification\")"]}, {"cell_type": "code", "execution_count": null, "id": "9a337ad1", "metadata": {}, "outputs": [], "source": ["# CRITICAL ISSUE ANALYSIS AND FIX\n", "# The ICP works on samples (1.52m) but fails on full data (28.54m)\n", "# This suggests fundamental data mismatch\n", "\n", "def analyze_data_mismatch(drone_points, ifc_points, transformation):\n", "    \"\"\"Analyze why ICP works on samples but fails on full dataset\"\"\"\n", "    \n", "    print(\"=== CRITICAL DATA MISMATCH ANALYSIS ===\")\n", "    \n", "    # Apply transformation to get aligned drone points\n", "    drone_transformed = np.dot(drone_points, transformation[:3, :3].T) + transformation[:3, 3]\n", "    \n", "    # 1. Check spatial coverage overlap\n", "    print(\"1. SPATIAL COVERAGE ANALYSIS:\")\n", "    \n", "    drone_bounds = {\n", "        'x_min': np.min(drone_transformed[:, 0]), 'x_max': np.max(drone_transformed[:, 0]),\n", "        'y_min': np.min(drone_transformed[:, 1]), 'y_max': np.max(drone_transformed[:, 1]),\n", "        'z_min': np.min(drone_transformed[:, 2]), 'z_max': np.max(drone_transformed[:, 2])\n", "    }\n", "    \n", "    ifc_bounds = {\n", "        'x_min': np.min(ifc_points[:, 0]), 'x_max': np.max(ifc_points[:, 0]),\n", "        'y_min': np.min(ifc_points[:, 1]), 'y_max': np.max(ifc_points[:, 1]),\n", "        'z_min': np.min(ifc_points[:, 2]), 'z_max': np.max(ifc_points[:, 2])\n", "    }\n", "    \n", "    print(f\"Drone bounds: X[{drone_bounds['x_min']:.1f}, {drone_bounds['x_max']:.1f}] \"\n", "          f\"Y[{drone_bounds['y_min']:.1f}, {drone_bounds['y_max']:.1f}] \"\n", "          f\"Z[{drone_bounds['z_min']:.1f}, {drone_bounds['z_max']:.1f}]\")\n", "    \n", "    print(f\"IFC bounds:   X[{ifc_bounds['x_min']:.1f}, {ifc_bounds['x_max']:.1f}] \"\n", "          f\"Y[{ifc_bounds['y_min']:.1f}, {ifc_bounds['y_max']:.1f}] \"\n", "          f\"Z[{ifc_bounds['z_min']:.1f}, {ifc_bounds['z_max']:.1f}]\")\n", "    \n", "    # Calculate overlap\n", "    x_overlap = max(0, min(drone_bounds['x_max'], ifc_bounds['x_max']) - \n", "                       max(drone_bounds['x_min'], ifc_bounds['x_min']))\n", "    y_overlap = max(0, min(drone_bounds['y_max'], ifc_bounds['y_max']) - \n", "                       max(drone_bounds['y_min'], ifc_bounds['y_min']))\n", "    \n", "    drone_area = (drone_bounds['x_max'] - drone_bounds['x_min']) * (drone_bounds['y_max'] - drone_bounds['y_min'])\n", "    ifc_area = (ifc_bounds['x_max'] - ifc_bounds['x_min']) * (ifc_bounds['y_max'] - ifc_bounds['y_min'])\n", "    overlap_area = x_overlap * y_overlap\n", "    \n", "    overlap_pct_drone = (overlap_area / drone_area * 100) if drone_area > 0 else 0\n", "    overlap_pct_ifc = (overlap_area / ifc_area * 100) if ifc_area > 0 else 0\n", "    \n", "    print(f\"Spatial overlap: {overlap_area:.0f} m² ({overlap_pct_drone:.1f}% of drone, {overlap_pct_ifc:.1f}% of IFC)\")\n", "    \n", "    # 2. Check point density distribution\n", "    print(f\"\\n2. POINT DENSITY ANALYSIS:\")\n", "    \n", "    drone_density = len(drone_points) / drone_area if drone_area > 0 else 0\n", "    ifc_density = len(ifc_points) / ifc_area if ifc_area > 0 else 0\n", "    \n", "    print(f\"Drone density: {drone_density:.2f} points/m²\")\n", "    print(f\"IFC density: {ifc_density:.2f} points/m²\")\n", "    print(f\"Density ratio: {ifc_density/drone_density:.1f}x denser IFC\" if drone_density > 0 else \"Cannot calculate\")\n", "    \n", "    # 3. Height distribution comparison\n", "    print(f\"\\n3. HEIGHT DISTRIBUTION ANALYSIS:\")\n", "    \n", "    drone_z_stats = {\n", "        'mean': np.mean(drone_transformed[:, 2]),\n", "        'std': np.std(drone_transformed[:, 2]),\n", "        'range': drone_bounds['z_max'] - drone_bounds['z_min']\n", "    }\n", "    \n", "    ifc_z_stats = {\n", "        'mean': np.mean(ifc_points[:, 2]),\n", "        'std': np.std(ifc_points[:, 2]),\n", "        'range': ifc_bounds['z_max'] - ifc_bounds['z_min']\n", "    }\n", "    \n", "    print(f\"Drone Z: mean={drone_z_stats['mean']:.1f}, std={drone_z_stats['std']:.1f}, range={drone_z_stats['range']:.1f}\")\n", "    print(f\"IFC Z:   mean={ifc_z_stats['mean']:.1f}, std={ifc_z_stats['std']:.1f}, range={ifc_z_stats['range']:.1f}\")\n", "    print(f\"Z offset: {ifc_z_stats['mean'] - drone_z_stats['mean']:.1f}m\")\n", "    \n", "    # 4. <PERSON><PERSON> in overlap region only\n", "    print(f\"\\n4. OVERLAP REGION EVALUATION:\")\n", "    \n", "    if overlap_area > 0:\n", "        # Define overlap bounds\n", "        overlap_x_min = max(drone_bounds['x_min'], ifc_bounds['x_min'])\n", "        overlap_x_max = min(drone_bounds['x_max'], ifc_bounds['x_max'])\n", "        overlap_y_min = max(drone_bounds['y_min'], ifc_bounds['y_min'])\n", "        overlap_y_max = min(drone_bounds['y_max'], ifc_bounds['y_max'])\n", "        \n", "        # Filter points to overlap region\n", "        drone_overlap_mask = ((drone_transformed[:, 0] >= overlap_x_min) & \n", "                             (drone_transformed[:, 0] <= overlap_x_max) &\n", "                             (drone_transformed[:, 1] >= overlap_y_min) & \n", "                             (drone_transformed[:, 1] <= overlap_y_max))\n", "        \n", "        ifc_overlap_mask = ((ifc_points[:, 0] >= overlap_x_min) & \n", "                           (ifc_points[:, 0] <= overlap_x_max) &\n", "                           (ifc_points[:, 1] >= overlap_y_min) & \n", "                           (ifc_points[:, 1] <= overlap_y_max))\n", "        \n", "        drone_overlap = drone_transformed[drone_overlap_mask]\n", "        ifc_overlap = ifc_points[ifc_overlap_mask]\n", "        \n", "        print(f\"Points in overlap - Drone: {len(drone_overlap):,}, IFC: {len(ifc_overlap):,}\")\n", "        \n", "        if len(drone_overlap) > 1000 and len(ifc_overlap) > 1000:\n", "            # Evaluate alignment in overlap region only\n", "            from scipy.spatial import cKDTree\n", "            \n", "            # Sample for evaluation\n", "            drone_sample_size = min(10000, len(drone_overlap))\n", "            ifc_sample_size = min(10000, len(ifc_overlap))\n", "            \n", "            drone_sample = drone_overlap[np.random.choice(len(drone_overlap), drone_sample_size, replace=False)]\n", "            ifc_sample = ifc_overlap[np.random.choice(len(ifc_overlap), ifc_sample_size, replace=False)]\n", "            \n", "            tree = cKDTree(ifc_sample)\n", "            distances, _ = tree.query(drone_sample)\n", "            \n", "            overlap_rmse = np.sqrt(np.mean(distances**2))\n", "            overlap_good_2m = np.sum(distances < 2.0) / len(distances) * 100\n", "            \n", "            print(f\"Overlap region RMSE: {overlap_rmse:.2f}m\")\n", "            print(f\"Overlap good points (<2m): {overlap_good_2m:.1f}%\")\n", "            \n", "            return {\n", "                'overlap_area': overlap_area,\n", "                'overlap_pct_drone': overlap_pct_drone,\n", "                'overlap_pct_ifc': overlap_pct_ifc,\n", "                'overlap_rmse': overlap_rmse,\n", "                'overlap_good_2m': overlap_good_2m,\n", "                'has_good_overlap': overlap_rmse < 10.0 and overlap_good_2m > 20\n", "            }\n", "    \n", "    return {\n", "        'overlap_area': overlap_area,\n", "        'overlap_pct_drone': overlap_pct_drone,\n", "        'overlap_pct_ifc': overlap_pct_ifc,\n", "        'has_good_overlap': False\n", "    }\n", "\n", "# Run the mismatch analysis\n", "mismatch_analysis = analyze_data_mismatch(drone_points, ifc_points, transformation)\n", "\n", "def targeted_solutions(mismatch_analysis):\n", "    \"\"\"Provide targeted solutions based on mismatch analysis\"\"\"\n", "    \n", "    print(f\"\\n{'='*80}\")\n", "    print(\"TARGETED SOLUTIONS BASED ON ANALYSIS\")\n", "    print(f\"{'='*80}\")\n", "    \n", "    if mismatch_analysis['overlap_pct_drone'] < 50:\n", "        print(\"❌ PROBLEM: Poor spatial overlap between datasets\")\n", "        print(\"🔧 SOLUTIONS:\")\n", "        print(\"   1. Verify coordinate systems match (UTM zone, datum)\")\n", "        print(\"   2. Check if drone covers the IFC model area\")\n", "        print(\"   3. Consider using only overlapping regions for alignment\")\n", "        \n", "    if mismatch_analysis.get('overlap_rmse', 100) > 15:\n", "        print(\"❌ PROBLEM: High error even in overlap regions\")\n", "        print(\"🔧 SOLUTIONS:\")\n", "        print(\"   1. Manual control point identification needed\")\n", "        print(\"   2. Check scale differences (units mismatch?)\")\n", "        print(\"   3. Verify Z-datum consistency\")\n", "    \n", "    if mismatch_analysis['has_good_overlap']:\n", "        print(\"✅ GOOD: Reasonable alignment in overlap regions\")\n", "        print(\"🔧 RECOMMENDATION: Focus alignment on overlap area only\")\n", "    \n", "    # Specific recommendations\n", "    print(f\"\\n📋 SPECIFIC NEXT STEPS:\")\n", "    \n", "    if mismatch_analysis['overlap_pct_drone'] > 70 and mismatch_analysis.get('overlap_rmse', 100) < 10:\n", "        print(\"   ✓ Proceed with overlap-focused alignment\")\n", "        return \"overlap_focused\"\n", "    elif mismatch_analysis['overlap_pct_drone'] > 30:\n", "        print(\"   ⚠ Try manual control point alignment\")\n", "        return \"manual_control_points\"\n", "    else:\n", "        print(\"   ❌ Fundamental coordinate system issues - needs investigation\")\n", "        return \"coordinate_system_check\"\n", "\n", "solution_type = targeted_solutions(mismatch_analysis)\n", "\n", "# SOLUTION 1: Overlap-focused alignment\n", "def overlap_focused_alignment(drone_points, ifc_points):\n", "    \"\"\"Align using only the overlapping regions\"\"\"\n", "    \n", "    print(f\"\\n=== OVERLAP-FOCUSED ALIGNMENT ===\")\n", "    \n", "    # Find overlap bounds\n", "    drone_bounds = {\n", "        'x_min': np.min(drone_points[:, 0]), 'x_max': np.max(drone_points[:, 0]),\n", "        'y_min': np.min(drone_points[:, 1]), 'y_max': np.max(drone_points[:, 1])\n", "    }\n", "    \n", "    ifc_bounds = {\n", "        'x_min': np.min(ifc_points[:, 0]), 'x_max': np.max(ifc_points[:, 0]),\n", "        'y_min': np.min(ifc_points[:, 1]), 'y_max': np.max(ifc_points[:, 1])\n", "    }\n", "    \n", "    # Define overlap region (intersection)\n", "    overlap_x_min = max(drone_bounds['x_min'], ifc_bounds['x_min'])\n", "    overlap_x_max = min(drone_bounds['x_max'], ifc_bounds['x_max'])\n", "    overlap_y_min = max(drone_bounds['y_min'], ifc_bounds['y_min'])\n", "    overlap_y_max = min(drone_bounds['y_max'], ifc_bounds['y_max'])\n", "    \n", "    print(f\"Overlap bounds: X[{overlap_x_min:.1f}, {overlap_x_max:.1f}] Y[{overlap_y_min:.1f}, {overlap_y_max:.1f}]\")\n", "    \n", "    # Filter both datasets to overlap region\n", "    drone_overlap_mask = ((drone_points[:, 0] >= overlap_x_min) & \n", "                         (drone_points[:, 0] <= overlap_x_max) &\n", "                         (drone_points[:, 1] >= overlap_y_min) & \n", "                         (drone_points[:, 1] <= overlap_y_max))\n", "    \n", "    ifc_overlap_mask = ((ifc_points[:, 0] >= overlap_x_min) & \n", "                       (ifc_points[:, 0] <= overlap_x_max) &\n", "                       (ifc_points[:, 1] >= overlap_y_min) & \n", "                       (ifc_points[:, 1] <= overlap_y_max))\n", "    \n", "    drone_overlap = drone_points[drone_overlap_mask]\n", "    ifc_overlap = ifc_points[ifc_overlap_mask]\n", "    \n", "    print(f\"Overlap points - Drone: {len(drone_overlap):,}, IFC: {len(ifc_overlap):,}\")\n", "    \n", "    if len(drone_overlap) < 1000 or len(ifc_overlap) < 1000:\n", "        print(\"❌ Insufficient overlap points for reliable alignment\")\n", "        return None, None\n", "    \n", "    # Smart sampling from overlap regions\n", "    def smart_overlap_sampling(points, target_size=15000):\n", "        if len(points) <= target_size:\n", "            return points\n", "        \n", "        # Stratified sampling by height for overlap region\n", "        z_values = points[:, 2]\n", "        n_strata = 3\n", "        samples = []\n", "        \n", "        for i in range(n_strata):\n", "            z_low = np.percentile(z_values, i * 100 / n_strata)\n", "            z_high = np.percentile(z_values, (i + 1) * 100 / n_strata)\n", "            \n", "            stratum_mask = (z_values >= z_low) & (z_values <= z_high)\n", "            stratum_points = points[stratum_mask]\n", "            \n", "            if len(stratum_points) > 0:\n", "                n_from_stratum = min(target_size // n_strata, len(stratum_points))\n", "                indices = np.random.choice(len(stratum_points), n_from_stratum, replace=False)\n", "                samples.append(stratum_points[indices])\n", "        \n", "        return np.vstack(samples) if samples else points[:target_size]\n", "    \n", "    # Sample from overlap regions\n", "    drone_sample = smart_overlap_sampling(drone_overlap, 15000)\n", "    ifc_sample = smart_overlap_sampling(ifc_overlap, 15000)\n", "    \n", "    print(f\"Sampled for alignment - Drone: {len(drone_sample):,}, IFC: {len(ifc_sample):,}\")\n", "    \n", "    # Create point clouds\n", "    source_pcd = o3d.geometry.PointCloud()\n", "    source_pcd.points = o3d.utility.Vector3dVector(drone_sample)\n", "    \n", "    target_pcd = o3d.geometry.PointCloud()\n", "    target_pcd.points = o3d.utility.Vector3dVector(ifc_sample)\n", "    \n", "    # Initial centroid alignment\n", "    source_center = np.mean(drone_sample, axis=0)\n", "    target_center = np.mean(ifc_sample, axis=0)\n", "    initial_offset = target_center - source_center\n", "    \n", "    # Apply initial alignment\n", "    drone_aligned = drone_sample + initial_offset\n", "    source_pcd.points = o3d.utility.Vector3dVector(drone_aligned)\n", "    \n", "    print(f\"Initial centroid offset: [{initial_offset[0]:.2f}, {initial_offset[1]:.2f}, {initial_offset[2]:.2f}]\")\n", "    \n", "    # Estimate normals\n", "    source_pcd.estimate_normals()\n", "    target_pcd.estimate_normals()\n", "    \n", "    # Multi-stage ICP on overlap region\n", "    # Stage 1: <PERSON><PERSON><PERSON>\n", "    coarse_result = o3d.pipelines.registration.registration_icp(\n", "        source_pcd, target_pcd,\n", "        max_correspondence_distance=5.0,\n", "        init=np.eye(4),\n", "        estimation_method=o3d.pipelines.registration.TransformationEstimationPointToPlane(),\n", "        criteria=o3d.pipelines.registration.ICPConvergenceCriteria(max_iteration=50)\n", "    )\n", "    \n", "    # Stage 2: Fine\n", "    fine_result = o3d.pipelines.registration.registration_icp(\n", "        source_pcd, target_pcd,\n", "        max_correspondence_distance=2.0,\n", "        init=coarse_result.transformation,\n", "        estimation_method=o3d.pipelines.registration.TransformationEstimationPointToPlane(),\n", "        criteria=o3d.pipelines.registration.ICPConvergenceCriteria(max_iteration=100)\n", "    )\n", "    \n", "    print(f\"Overlap ICP - Coarse: Fitness {coarse_result.fitness:.4f}, RMSE {coarse_result.inlier_rmse:.2f}m\")\n", "    print(f\"Overlap ICP - Fine: Fitness {fine_result.fitness:.4f}, RMSE {fine_result.inlier_rmse:.2f}m\")\n", "    \n", "    # Combine transformations\n", "    final_transformation = fine_result.transformation.copy()\n", "    final_transformation[:3, 3] += initial_offset\n", "    \n", "    return final_transformation, fine_result\n", "\n", "# Try overlap-focused alignment if recommended\n", "if solution_type == \"overlap_focused\":\n", "    print(\"\\nTrying overlap-focused alignment...\")\n", "    overlap_transformation, overlap_result = overlap_focused_alignment(drone_points, ifc_points)\n", "    \n", "    if overlap_transformation is not None:\n", "        # Evaluate overlap-focused results\n", "        overlap_eval, _, _ = regional_evaluation(drone_points, ifc_points, overlap_transformation)\n", "        \n", "        print(f\"\\n=== OVERLAP-FOCUSED RESULTS ===\")\n", "        print(f\"RMSE: {overlap_eval['rmse']:.2f}m\")\n", "        print(f\"Good points (<5m): {overlap_eval['good_points_5m']:.1f}%\")\n", "        \n", "        if overlap_eval['rmse'] < 15.0:\n", "            print(\"✅ OVERLAP-FOCUSED ALIGNMENT SUCCESSFUL!\")\n", "            print(f\"Improvement from {28.54:.2f}m to {overlap_eval['rmse']:.2f}m\")\n", "        else:\n", "            print(\"⚠ Still high RMSE - coordinate system issues likely\")\n", "\n", "# SOLUTION 2: Manual control points guidance\n", "def manual_control_points_guidance():\n", "    \"\"\"Provide guidance for manual control point identification\"\"\"\n", "    \n", "    print(f\"\\n{'='*80}\")\n", "    print(\"MANUAL CONTROL POINTS GUIDANCE\")\n", "    print(f\"{'='*80}\")\n", "    \n", "    print(\"📍 RECOMMENDED CONTROL POINTS TO IDENTIFY:\")\n", "    print(\"   1. Building corners (especially foundation corners)\")\n", "    print(\"   2. Distinctive pile groups or pile intersections\")\n", "    print(\"   3. Road intersections or infrastructure features\")\n", "    print(\"   4. Equipment or temporary structures visible in both datasets\")\n", "    print(\"   5. Survey monuments or known reference points\")\n", "    \n", "    print(f\"\\n🎯 CONTROL POINT STRATEGY:\")\n", "    print(\"   • Identify 4-6 well-distributed points across the site\")\n", "    print(\"   • Ensure points are geometrically stable (corners, not edges)\")\n", "    print(\"   • Manually measure coordinates in both datasets\")\n", "    print(\"   • Use these for initial transformation, then refine with ICP\")\n", "    \n", "    print(f\"\\n💻 IMPLEMENTATION:\")\n", "    print(\"   • Use CloudCompare or similar software for manual picking\")\n", "    print(\"   • Export control point coordinates\")\n", "    print(\"   • Calculate transformation matrix from control points\")\n", "    print(\"   • Apply to full dataset and evaluate\")\n", "\n", "if solution_type == \"manual_control_points\":\n", "    manual_control_points_guidance()\n", "\n", "print(f\"\\n{'='*80}\")\n", "print(\"SUMMARY AND RECOMMENDATIONS\")\n", "print(f\"{'='*80}\")\n", "\n", "if mismatch_analysis.get('overlap_rmse', 100) < 10:\n", "    print(\"✅ GOOD NEWS: The alignment algorithm works well\")\n", "    print(\"❌ ISSUE: Limited spatial overlap or coordinate system mismatch\")\n", "    print(\"🔧 SOLUTION: Focus on overlap regions or verify coordinate systems\")\n", "else:\n", "    print(\"❌ FUNDAMENTAL ISSUE: Poor alignment even in overlap regions\")\n", "    print(\"🔧 SOLUTIONS NEEDED:\")\n", "    print(\"   1. Coordinate system verification (projection, datum, units)\")\n", "    print(\"   2. Manual control point identification\")\n", "    print(\"   3. Scale verification (check if units match)\")\n", "    print(\"   4. Temporal alignment (construction phase matching)\")"]}, {"cell_type": "code", "execution_count": null, "id": "25225a89", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "564eb6ee", "metadata": {}, "outputs": [], "source": ["# CONSTRUCTION PHASE ALIGNMENT STRATEGY\n", "# Problem: IFC (designed) vs Drone (as-built) geometric differences\n", "\n", "def analyze_construction_phase_mismatch(drone_points, ifc_points):\n", "    \"\"\"Analyze what's different between designed IFC and as-built drone data\"\"\"\n", "    \n", "    print(\"=== CONSTRUCTION PHASE MISMATCH ANALYSIS ===\")\n", "    \n", "    # 1. Height layer analysis\n", "    print(\"1. CONSTRUCTION PROGRESS BY HEIGHT:\")\n", "    \n", "    # Analyze what exists at different heights\n", "    drone_z = drone_points[:, 2]\n", "    ifc_z = ifc_points[:, 2]\n", "    \n", "    # Define height layers\n", "    min_z = min(np.min(drone_z), np.min(ifc_z))\n", "    max_z = max(np.max(drone_z), np.max(ifc_z))\n", "    \n", "    height_layers = {\n", "        'foundation': (min_z, min_z + 2),\n", "        'ground_floor': (min_z + 2, min_z + 5),\n", "        'upper_levels': (min_z + 5, max_z)\n", "    }\n", "    \n", "    for layer_name, (z_min, z_max) in height_layers.items():\n", "        drone_layer_mask = (drone_z >= z_min) & (drone_z <= z_max)\n", "        ifc_layer_mask = (ifc_z >= z_min) & (ifc_z <= z_max)\n", "        \n", "        drone_layer_count = np.sum(drone_layer_mask)\n", "        ifc_layer_count = np.sum(ifc_layer_mask)\n", "        \n", "        if ifc_layer_count > 0:\n", "            completion_ratio = drone_layer_count / ifc_layer_count\n", "        else:\n", "            completion_ratio = 0\n", "        \n", "        print(f\"  {layer_name:12}: Drone {drone_layer_count:7,} | IFC {ifc_layer_count:7,} | Ratio {completion_ratio:.2f}\")\n", "    \n", "    # 2. Identify what should align well (foundation level)\n", "    print(f\"\\n2. FOUNDATION LEVEL ANALYSIS:\")\n", "    \n", "    # Focus on foundation level (bottom 2m)\n", "    foundation_height = min_z + 2\n", "    \n", "    drone_foundation_mask = drone_z <= foundation_height\n", "    ifc_foundation_mask = ifc_z <= foundation_height\n", "    \n", "    drone_foundation = drone_points[drone_foundation_mask]\n", "    ifc_foundation = ifc_points[ifc_foundation_mask]\n", "    \n", "    print(f\"Foundation points - Drone: {len(drone_foundation):,}, IFC: {len(ifc_foundation):,}\")\n", "    \n", "    if len(drone_foundation) > 1000 and len(ifc_foundation) > 1000:\n", "        # Test alignment on foundation only\n", "        from scipy.spatial import cKDTree\n", "        \n", "        # Sample foundation points\n", "        drone_found_sample = drone_foundation[np.random.choice(len(drone_foundation), \n", "                                                             min(5000, len(drone_foundation)), \n", "                                                             replace=False)]\n", "        ifc_found_sample = ifc_foundation[np.random.choice(len(ifc_foundation), \n", "                                                         min(5000, len(ifc_foundation)), \n", "                                                         replace=False)]\n", "        \n", "        tree = cKDTree(ifc_found_sample)\n", "        distances, _ = tree.query(drone_found_sample)\n", "        \n", "        foundation_rmse = np.sqrt(np.mean(distances**2))\n", "        foundation_good_2m = np.sum(distances < 2.0) / len(distances) * 100\n", "        \n", "        print(f\"Foundation-only RMSE: {foundation_rmse:.2f}m\")\n", "        print(f\"Foundation good points (<2m): {foundation_good_2m:.1f}%\")\n", "        \n", "        return {\n", "            'foundation_rmse': foundation_rmse,\n", "            'foundation_good_2m': foundation_good_2m,\n", "            'should_use_foundation_only': foundation_rmse < 10.0 and foundation_good_2m > 15\n", "        }\n", "    \n", "    return {'should_use_foundation_only': False}\n", "\n", "def foundation_only_alignment(drone_points, ifc_points):\n", "    \"\"\"Align using only foundation-level elements\"\"\"\n", "    \n", "    print(\"=== FOUNDATION-ONLY ALIGNMENT ===\")\n", "    \n", "    # Extract foundation level points (bottom 2m of each dataset)\n", "    drone_z = drone_points[:, 2]\n", "    ifc_z = ifc_points[:, 2]\n", "    \n", "    drone_foundation_height = np.percentile(drone_z, 95)  # Bottom 95% \n", "    ifc_foundation_height = np.percentile(ifc_z, 95)\n", "    \n", "    drone_foundation_mask = drone_z <= drone_foundation_height\n", "    ifc_foundation_mask = ifc_z <= ifc_foundation_height\n", "    \n", "    drone_foundation = drone_points[drone_foundation_mask]\n", "    ifc_foundation = ifc_points[ifc_foundation_mask]\n", "    \n", "    print(f\"Foundation points - Drone: {len(drone_foundation):,}, IFC: {len(ifc_foundation):,}\")\n", "    \n", "    # Smart sampling preserving spatial distribution\n", "    def foundation_sampling(points, target_size=15000):\n", "        if len(points) <= target_size:\n", "            return points\n", "        \n", "        # Grid-based sampling to preserve foundation layout\n", "        min_coords = np.min(points, axis=0)\n", "        max_coords = np.max(points, axis=0)\n", "        \n", "        grid_size = 5.0  # 5m grid for foundation elements\n", "        x_bins = np.arange(min_coords[0], max_coords[0] + grid_size, grid_size)\n", "        y_bins = np.arange(min_coords[1], max_coords[1] + grid_size, grid_size)\n", "        \n", "        points_per_cell = target_size // (len(x_bins) * len(y_bins))\n", "        points_per_cell = max(1, points_per_cell)\n", "        \n", "        grid_samples = []\n", "        for i in range(len(x_bins) - 1):\n", "            for j in range(len(y_bins) - 1):\n", "                mask = ((points[:, 0] >= x_bins[i]) & (points[:, 0] < x_bins[i+1]) &\n", "                       (points[:, 1] >= y_bins[j]) & (points[:, 1] < y_bins[j+1]))\n", "                \n", "                cell_points = points[mask]\n", "                if len(cell_points) > 0:\n", "                    n_sample = min(points_per_cell, len(cell_points))\n", "                    indices = np.random.choice(len(cell_points), n_sample, replace=False)\n", "                    grid_samples.append(cell_points[indices])\n", "        \n", "        if grid_samples:\n", "            return np.vstack(grid_samples)[:target_size]\n", "        else:\n", "            indices = np.random.choice(len(points), target_size, replace=False)\n", "            return points[indices]\n", "    \n", "    # Sample foundation points\n", "    drone_sample = foundation_sampling(drone_foundation, 15000)\n", "    ifc_sample = foundation_sampling(ifc_foundation, 15000)\n", "    \n", "    print(f\"Foundation samples - Drone: {len(drone_sample):,}, IFC: {len(ifc_sample):,}\")\n", "    \n", "    # Create point clouds\n", "    source_pcd = o3d.geometry.PointCloud()\n", "    source_pcd.points = o3d.utility.Vector3dVector(drone_sample)\n", "    \n", "    target_pcd = o3d.geometry.PointCloud()\n", "    target_pcd.points = o3d.utility.Vector3dVector(ifc_sample)\n", "    \n", "    # Remove outliers (important for foundation alignment)\n", "    source_pcd, _ = source_pcd.remove_statistical_outlier(nb_neighbors=20, std_ratio=1.5)\n", "    target_pcd, _ = target_pcd.remove_statistical_outlier(nb_neighbors=20, std_ratio=1.5)\n", "    \n", "    print(f\"After outlier removal - Source: {len(source_pcd.points)}, Target: {len(target_pcd.points)}\")\n", "    \n", "    # Estimate normals\n", "    source_pcd.estimate_normals()\n", "    target_pcd.estimate_normals()\n", "    \n", "    # Foundation-specific ICP\n", "    print(\"Running foundation-specific ICP...\")\n", "    \n", "    # Stage 1: Coarse alignment\n", "    coarse_result = o3d.pipelines.registration.registration_icp(\n", "        source_pcd, target_pcd,\n", "        max_correspondence_distance=3.0,  # Reasonable for foundation elements\n", "        init=np.eye(4),\n", "        estimation_method=o3d.pipelines.registration.TransformationEstimationPointToPlane(),\n", "        criteria=o3d.pipelines.registration.ICPConvergenceCriteria(max_iteration=50)\n", "    )\n", "    \n", "    # Stage 2: Fine alignment\n", "    fine_result = o3d.pipelines.registration.registration_icp(\n", "        source_pcd, target_pcd,\n", "        max_correspondence_distance=1.5,\n", "        init=coarse_result.transformation,\n", "        estimation_method=o3d.pipelines.registration.TransformationEstimationPointToPlane(),\n", "        criteria=o3d.pipelines.registration.ICPConvergenceCriteria(max_iteration=100)\n", "    )\n", "    \n", "    print(f\"Foundation ICP - Coarse: Fitness {coarse_result.fitness:.4f}, RMSE {coarse_result.inlier_rmse:.2f}m\")\n", "    print(f\"Foundation ICP - Fine: Fitness {fine_result.fitness:.4f}, RMSE {fine_result.inlier_rmse:.2f}m\")\n", "    \n", "    return fine_result.transformation, fine_result\n", "\n", "def pile_center_alignment(drone_points, ifc_points):\n", "    \"\"\"Align using pile centers only (most reliable for construction sites)\"\"\"\n", "    \n", "    print(\"=== PILE CENTER ALIGNMENT ===\")\n", "    \n", "    # Extract pile-like points (ground level, clustered)\n", "    def extract_pile_centers(points, grid_size=3.0):\n", "        \"\"\"Extract pile center points using grid clustering\"\"\"\n", "        \n", "        # Use only bottom points (likely piles)\n", "        z_values = points[:, 2]\n", "        ground_height = np.percentile(z_values, 10)  # Bottom 10%\n", "        ground_mask = np.abs(z_values - ground_height) < 1.0\n", "        ground_points = points[ground_mask]\n", "        \n", "        if len(ground_points) < 100:\n", "            return np.empty((0, 3))\n", "        \n", "        # Grid-based clustering to find pile centers\n", "        min_coords = np.min(ground_points, axis=0)\n", "        max_coords = np.max(ground_points, axis=0)\n", "        \n", "        x_bins = np.arange(min_coords[0], max_coords[0] + grid_size, grid_size)\n", "        y_bins = np.arange(min_coords[1], max_coords[1] + grid_size, grid_size)\n", "        \n", "        pile_centers = []\n", "        \n", "        for i in range(len(x_bins) - 1):\n", "            for j in range(len(y_bins) - 1):\n", "                mask = ((ground_points[:, 0] >= x_bins[i]) & (ground_points[:, 0] < x_bins[i+1]) &\n", "                       (ground_points[:, 1] >= y_bins[j]) & (ground_points[:, 1] < y_bins[j+1]))\n", "                \n", "                cell_points = ground_points[mask]\n", "                if len(cell_points) > 5:  # Minimum points to be considered a pile\n", "                    # Center of mass as pile center\n", "                    pile_center = np.mean(cell_points, axis=0)\n", "                    pile_centers.append(pile_center)\n", "        \n", "        return np.array(pile_centers) if pile_centers else np.empty((0, 3))\n", "    \n", "    # Extract pile centers from both datasets\n", "    drone_pile_centers = extract_pile_centers(drone_points, 3.0)\n", "    ifc_pile_centers = extract_pile_centers(ifc_points, 3.0)\n", "    \n", "    print(f\"Pile centers - Drone: {len(drone_pile_centers)}, IFC: {len(ifc_pile_centers)}\")\n", "    \n", "    if len(drone_pile_centers) < 10 or len(ifc_pile_centers) < 10:\n", "        print(\"❌ Insufficient pile centers found\")\n", "        return None, None\n", "    \n", "    # Create point clouds from pile centers\n", "    source_pcd = o3d.geometry.PointCloud()\n", "    source_pcd.points = o3d.utility.Vector3dVector(drone_pile_centers)\n", "    \n", "    target_pcd = o3d.geometry.PointCloud()\n", "    target_pcd.points = o3d.utility.Vector3dVector(ifc_pile_centers)\n", "    \n", "    # Pile center ICP (use point-to-point for discrete points)\n", "    print(\"Running pile center ICP...\")\n", "    \n", "    icp_result = o3d.pipelines.registration.registration_icp(\n", "        source_pcd, target_pcd,\n", "        max_correspondence_distance=5.0,  # <PERSON><PERSON> should be close\n", "        init=np.eye(4),\n", "        estimation_method=o3d.pipelines.registration.TransformationEstimationPointToPoint(),\n", "        criteria=o3d.pipelines.registration.ICPConvergenceCriteria(max_iteration=100)\n", "    )\n", "    \n", "    print(f\"Pile center ICP - Fitness: {icp_result.fitness:.4f}, RMSE: {icp_result.inlier_rmse:.2f}m\")\n", "    \n", "    return icp_result.transformation, icp_result\n", "\n", "# Run construction phase analysis\n", "print(\"Analyzing construction phase mismatch...\")\n", "phase_analysis = analyze_construction_phase_mismatch(drone_points, ifc_points)\n", "\n", "# Try different alignment strategies\n", "strategies_to_try = []\n", "\n", "if phase_analysis.get('should_use_foundation_only', False):\n", "    strategies_to_try.append(('foundation_only', foundation_only_alignment))\n", "\n", "strategies_to_try.append(('pile_centers', pile_center_alignment))\n", "\n", "# Test each strategy\n", "best_rmse = float('inf')\n", "best_transformation = None\n", "best_method = None\n", "\n", "for strategy_name, strategy_func in strategies_to_try:\n", "    print(f\"\\n{'='*60}\")\n", "    print(f\"TESTING STRATEGY: {strategy_name.upper()}\")\n", "    print(f\"{'='*60}\")\n", "    \n", "    try:\n", "        transformation, icp_result = strategy_func(drone_points, ifc_points)\n", "        \n", "        if transformation is not None:\n", "            # Quick evaluation\n", "            drone_transformed = np.dot(drone_points, transformation[:3, :3].T) + transformation[:3, 3]\n", "            \n", "            # Sample for evaluation\n", "            n_eval = min(20000, len(drone_transformed))\n", "            drone_eval = drone_transformed[np.random.choice(len(drone_transformed), n_eval, replace=False)]\n", "            ifc_eval = ifc_points[np.random.choice(len(ifc_points), min(20000, len(ifc_points)), replace=False)]\n", "            \n", "            from scipy.spatial import cKDTree\n", "            tree = cKDTree(ifc_eval)\n", "            distances, _ = tree.query(drone_eval)\n", "            \n", "            strategy_rmse = np.sqrt(np.mean(distances**2))\n", "            strategy_good_2m = np.sum(distances < 2.0) / len(distances) * 100\n", "            strategy_good_5m = np.sum(distances < 5.0) / len(distances) * 100\n", "            \n", "            print(f\"Strategy {strategy_name} results:\")\n", "            print(f\"  RMSE: {strategy_rmse:.2f}m\")\n", "            print(f\"  Good points (<2m): {strategy_good_2m:.1f}%\")\n", "            print(f\"  Good points (<5m): {strategy_good_5m:.1f}%\")\n", "            \n", "            if strategy_rmse < best_rmse:\n", "                best_rmse = strategy_rmse\n", "                best_transformation = transformation\n", "                best_method = strategy_name\n", "            \n", "    except Exception as e:\n", "        print(f\"Strategy {strategy_name} failed: {str(e)}\")\n", "\n", "# Report best result\n", "print(f\"\\n{'='*80}\")\n", "print(\"BEST CONSTRUCTION-AWARE ALIGNMENT RESULT\")\n", "print(f\"{'='*80}\")\n", "\n", "if best_transformation is not None:\n", "    print(f\"Best method: {best_method}\")\n", "    print(f\"Best RMSE: {best_rmse:.2f}m\")\n", "    print(f\"Improvement from original: {28.54 - best_rmse:.2f}m\")\n", "    \n", "    if best_rmse < 15.0:\n", "        print(\"✅ SIGNIFICANT IMPROVEMENT ACHIEVED!\")\n", "        print(\"This suggests the datasets CAN be aligned when focusing on the right features\")\n", "    elif best_rmse < 20.0:\n", "        print(\"⚠ MODERATE IMPROVEMENT\")\n", "        print(\"Manual control points may still be needed for best results\")\n", "    else:\n", "        print(\"❌ LIMITED IMPROVEMENT\")\n", "        print(\"Fundamental issues remain - manual intervention required\")\n", "        \n", "    # Save the best result\n", "    if best_rmse < 20.0:\n", "        print(f\"\\nSaving best alignment result ({best_method})...\")\n", "        \n", "        best_results = {\n", "            'method': f'construction_aware_{best_method}',\n", "            'rmse': best_rmse,\n", "            'transformation_matrix': best_transformation.tolist(),\n", "            'improvement_from_original': 28.54 - best_rmse,\n", "            'analysis': 'IFC represents designed structures, drone shows as-built reality'\n", "        }\n", "        \n", "        import json\n", "        with open(output_path / f\"{site_name}_construction_aware_alignment.json\", 'w') as f:\n", "            json.dump(best_results, f, indent=2)\n", "        \n", "        print(f\"Best results saved to: {output_path}\")\n", "\n", "else:\n", "    print(\"❌ NO SUITABLE ALIGNMENT FOUND\")\n", "    print(\"Manual control point identification is required\")\n", "\n", "print(f\"\\n{'='*80}\")\n", "print(\"FINAL RECOMMENDATION\")\n", "print(f\"{'='*80}\")\n", "\n", "if best_rmse < 10.0:\n", "    print(\"✅ PROCEED with construction-aware alignment\")\n", "    print(\"The key insight: Focus on foundation/pile elements that should match\")\n", "elif best_rmse < 20.0:\n", "    print(\"⚠ PARTIAL SUCCESS - consider hybrid approach:\")\n", "    print(\"1. Use this alignment as starting point\")\n", "    print(\"2. Add 3-4 manual control points for refinement\")\n", "    print(\"3. Re-run ICP with manual constraints\")\n", "else:\n", "    print(\"❌ MANUAL INTERVENTION REQUIRED:\")\n", "    print(\"1. The IFC design differs significantly from as-built reality\")\n", "    print(\"2. Identify matching elements manually (pile centers, building corners)\")\n", "    print(\"3. Use manual control points for initial alignment\")\n", "    print(\"4. Consider if datasets represent different construction phases\")"]}, {"cell_type": "code", "execution_count": null, "id": "6ed5827f", "metadata": {}, "outputs": [], "source": ["# DEFINITIVE SOLUTION FOR CONSTRUCTION PHASE MISMATCH\n", "# Problem: Drone shows foundation/site prep, IFC shows superstructure\n", "\n", "def visualize_phase_mismatch(drone_points, ifc_points):\n", "    \"\"\"Create clear visualization of the phase mismatch\"\"\"\n", "    \n", "    print(\"=== PHASE MISMATCH VISUALIZATION ===\")\n", "    \n", "    import matplotlib.pyplot as plt\n", "    from mpl_toolkits.mplot3d import Axes3D\n", "    \n", "    # Sample for visualization\n", "    drone_sample = drone_points[::len(drone_points)//5000][:5000]\n", "    ifc_sample = ifc_points[::len(ifc_points)//5000][:5000]\n", "    \n", "    fig = plt.figure(figsize=(20, 12))\n", "    \n", "    # Side view (XZ plane)\n", "    ax1 = fig.add_subplot(2, 3, 1)\n", "    ax1.scatter(drone_sample[:, 0], drone_sample[:, 2], c='red', s=1, alpha=0.6, label='Drone (Foundation)')\n", "    ax1.scatter(ifc_sample[:, 0], ifc_sample[:, 2], c='blue', s=1, alpha=0.6, label='IFC (Superstructure)')\n", "    ax1.set_xlabel('X (Easting)')\n", "    ax1.set_ylabel('Z (Height)')\n", "    ax1.set_title('Side View - Shows Vertical Separation')\n", "    ax1.legend()\n", "    ax1.grid(True, alpha=0.3)\n", "    \n", "    # Top view (XY plane)\n", "    ax2 = fig.add_subplot(2, 3, 2)\n", "    ax2.scatter(drone_sample[:, 0], drone_sample[:, 1], c='red', s=1, alpha=0.6, label='Drone')\n", "    ax2.scatter(ifc_sample[:, 0], ifc_sample[:, 1], c='blue', s=1, alpha=0.6, label='IFC')\n", "    ax2.set_xlabel('X (Easting)')\n", "    ax2.set_ylabel('Y (Northing)')\n", "    ax2.set_title('Top View - Spatial Overlap')\n", "    ax2.legend()\n", "    ax2.axis('equal')\n", "    \n", "    # Height distribution\n", "    ax3 = fig.add_subplot(2, 3, 3)\n", "    ax3.hist(drone_sample[:, 2], bins=30, alpha=0.7, color='red', label='Drone', density=True)\n", "    ax3.hist(ifc_sample[:, 2], bins=30, alpha=0.7, color='blue', label='IFC', density=True)\n", "    ax3.set_xlabel('Height (m)')\n", "    ax3.set_ylabel('Density')\n", "    ax3.set_title('Height Distributions - No Overlap!')\n", "    ax3.legend()\n", "    ax3.grid(True, alpha=0.3)\n", "    \n", "    # 3D view\n", "    ax4 = fig.add_subplot(2, 3, 4, projection='3d')\n", "    ax4.scatter(drone_sample[:, 0], drone_sample[:, 1], drone_sample[:, 2], \n", "               c='red', s=0.5, alpha=0.4, label='Drone')\n", "    ax4.scatter(ifc_sample[:, 0], ifc_sample[:, 1], ifc_sample[:, 2], \n", "               c='blue', s=0.5, alpha=0.4, label='IFC')\n", "    ax4.set_xlabel('X')\n", "    ax4.set_ylabel('Y')\n", "    ax4.set_zlabel('Z')\n", "    ax4.set_title('3D View - Complete Separation')\n", "    ax4.legend()\n", "    \n", "    # Analysis summary\n", "    ax5 = fig.add_subplot(2, 3, 5)\n", "    ax5.axis('off')\n", "    \n", "    drone_z_stats = f\"\"\"\n", "DRONE SCAN ANALYSIS:\n", "• Points: {len(drone_points):,}\n", "• Height range: {np.min(drone_points[:, 2]):.1f} - {np.max(drone_points[:, 2]):.1f} m\n", "• Mean height: {np.mean(drone_points[:, 2]):.1f} m\n", "• Represents: FOUNDATION/SITE PREP PHASE\n", "\n", "Content breakdown:\n", "- Foundation level: {np.sum(drone_points[:, 2] <= np.percentile(drone_points[:, 2], 43))//1000}K pts\n", "- Ground structures: {np.sum((drone_points[:, 2] > np.percentile(drone_points[:, 2], 43)) & \n", "                           (drone_points[:, 2] <= np.percentile(drone_points[:, 2], 98)))//1000}K pts  \n", "- Equipment/temp: {np.sum(drone_points[:, 2] > np.percentile(drone_points[:, 2], 98))//1000}K pts\n", "    \"\"\"\n", "    \n", "    ifc_z_stats = f\"\"\"\n", "IFC MODEL ANALYSIS:\n", "• Points: {len(ifc_points):,}\n", "• Height range: {np.min(ifc_points[:, 2]):.1f} - {np.max(ifc_points[:, 2]):.1f} m\n", "• Mean height: {np.mean(ifc_points[:, 2]):.1f} m\n", "• Represents: BUILDING SUPERSTRUCTURE\n", "\n", "All points are ABOVE ground level!\n", "This is the designed building structure\n", "that will be built on top of the current\n", "foundation work shown in drone data.\n", "    \"\"\"\n", "    \n", "    ax5.text(0.02, 0.98, drone_z_stats, transform=ax5.transAxes, fontsize=9,\n", "             verticalalignment='top', fontfamily='monospace', color='darkred')\n", "    \n", "    ax6 = fig.add_subplot(2, 3, 6)\n", "    ax6.axis('off')\n", "    ax6.text(0.02, 0.98, ifc_z_stats, transform=ax6.transAxes, fontsize=9,\n", "             verticalalignment='top', fontfamily='monospace', color='darkblue')\n", "    \n", "    plt.tight_layout()\n", "    plt.suptitle('CONSTRUCTION PHASE MISMATCH: Foundation vs Superstructure', \n", "                 fontsize=16, y=0.98)\n", "    plt.show()\n", "    \n", "    return fig\n", "\n", "def create_alignment_strategy_for_phases(drone_points, ifc_points):\n", "    \"\"\"Create alignment strategy recognizing phase differences\"\"\"\n", "    \n", "    print(\"=== PHASE-AWARE ALIGNMENT STRATEGY ===\")\n", "    \n", "    # Strategy 1: Project IFC to foundation level\n", "    print(\"Strategy 1: Project IFC footprint to foundation level\")\n", "    \n", "    # Get building footprint from IFC by projecting to ground\n", "    ifc_xy = ifc_points[:, :2]  # Just X,Y coordinates\n", "    \n", "    # Find the foundation level from drone data\n", "    drone_foundation_height = np.percentile(drone_points[:, 2], 15)  # Bottom 15%\n", "    print(f\"Estimated foundation level: {drone_foundation_height:.1f}m\")\n", "    \n", "    # Create \"projected IFC footprint\" at foundation level\n", "    ifc_footprint_z = np.full(len(ifc_xy), drone_foundation_height)\n", "    ifc_projected = np.column_stack([ifc_xy, ifc_footprint_z])\n", "    \n", "    # Sample to reasonable size\n", "    if len(ifc_projected) > 20000:\n", "        indices = np.random.choice(len(ifc_projected), 20000, replace=False)\n", "        ifc_projected = ifc_projected[indices]\n", "    \n", "    print(f\"Created IFC footprint projection: {len(ifc_projected):,} points at Z={drone_foundation_height:.1f}m\")\n", "    \n", "    # Strategy 2: Extract drone foundation footprint\n", "    print(\"\\nStrategy 2: Extract drone foundation footprint\")\n", "    \n", "    # Get drone points near foundation level\n", "    foundation_tolerance = 2.0  # Within 2m of foundation level\n", "    drone_foundation_mask = np.abs(drone_points[:, 2] - drone_foundation_height) < foundation_tolerance\n", "    drone_foundation = drone_points[drone_foundation_mask]\n", "    \n", "    print(f\"Drone foundation points: {len(drone_foundation):,}\")\n", "    \n", "    # Sample drone foundation to reasonable size\n", "    if len(drone_foundation) > 20000:\n", "        indices = np.random.choice(len(drone_foundation), 20000, replace=False)\n", "        drone_foundation = drone_foundation[indices]\n", "    \n", "    # Strategy 3: Footprint alignment (2D + Z constraint)\n", "    print(\"\\nStrategy 3: Building footprint alignment\")\n", "    \n", "    # Create point clouds\n", "    source_pcd = o3d.geometry.PointCloud()\n", "    source_pcd.points = o3d.utility.Vector3dVector(drone_foundation)\n", "    \n", "    target_pcd = o3d.geometry.PointCloud()\n", "    target_pcd.points = o3d.utility.Vector3dVector(ifc_projected)\n", "    \n", "    # Downsample for performance\n", "    source_pcd = source_pcd.voxel_down_sample(0.5)\n", "    target_pcd = target_pcd.voxel_down_sample(0.5)\n", "    \n", "    print(f\"Downsampled - Source: {len(source_pcd.points)}, Target: {len(target_pcd.points)}\")\n", "    \n", "    # Remove outliers\n", "    source_pcd, _ = source_pcd.remove_statistical_outlier(nb_neighbors=20, std_ratio=2.0)\n", "    target_pcd, _ = target_pcd.remove_statistical_outlier(nb_neighbors=20, std_ratio=2.0)\n", "    \n", "    # Initial centroid alignment (XY only)\n", "    source_center = np.mean(np.asarray(source_pcd.points), axis=0)\n", "    target_center = np.mean(np.asarray(target_pcd.points), axis=0)\n", "    \n", "    # Only align X<PERSON>, keep Z as-is\n", "    xy_offset = target_center[:2] - source_center[:2]\n", "    initial_offset = np.array([xy_offset[0], xy_offset[1], 0.0])\n", "    \n", "    print(f\"Initial XY offset: [{initial_offset[0]:.2f}, {initial_offset[1]:.2f}]\")\n", "    \n", "    # Apply initial alignment\n", "    source_points_aligned = np.asarray(source_pcd.points) + initial_offset\n", "    source_pcd.points = o3d.utility.Vector3dVector(source_points_aligned)\n", "    \n", "    # Footprint ICP (constrained to XY plane)\n", "    print(\"Running footprint ICP...\")\n", "    \n", "    # Use point-to-point for 2D footprint matching\n", "    icp_result = o3d.pipelines.registration.registration_icp(\n", "        source_pcd, target_pcd,\n", "        max_correspondence_distance=3.0,\n", "        init=np.eye(4),\n", "        estimation_method=o3d.pipelines.registration.TransformationEstimationPointToPoint(),\n", "        criteria=o3d.pipelines.registration.ICPConvergenceCriteria(max_iteration=100)\n", "    )\n", "    \n", "    print(f\"Footprint ICP - Fitness: {icp_result.fitness:.4f}, RMSE: {icp_result.inlier_rmse:.2f}m\")\n", "    \n", "    # Create final transformation (XY alignment only)\n", "    final_transformation = np.eye(4)\n", "    final_transformation[:2, 3] = initial_offset[:2] + icp_result.transformation[:2, 3]\n", "    # Keep Z transformation minimal - just match foundation levels\n", "    final_transformation[2, 3] = drone_foundation_height - np.mean(ifc_points[:, 2])\n", "    \n", "    return final_transformation, icp_result, drone_foundation_height\n", "\n", "def evaluate_phase_aware_alignment(drone_points, ifc_points, transformation, foundation_height):\n", "    \"\"\"Evaluate phase-aware alignment\"\"\"\n", "    \n", "    print(\"=== PHASE-AWARE ALIGNMENT EVALUATION ===\")\n", "    \n", "    # Apply transformation to drone points\n", "    drone_transformed = np.dot(drone_points, transformation[:3, :3].T) + transformation[:3, 3]\n", "    \n", "    # Evaluation 1: Foundation level alignment\n", "    print(\"1. FOUNDATION LEVEL EVALUATION:\")\n", "    \n", "    # Get drone foundation points\n", "    foundation_tolerance = 2.0\n", "    drone_foundation_mask = np.abs(drone_points[:, 2] - foundation_height) < foundation_tolerance\n", "    drone_foundation_aligned = drone_transformed[drone_foundation_mask]\n", "    \n", "    # Project IFC to foundation level for comparison\n", "    ifc_at_foundation = ifc_points.copy()\n", "    ifc_at_foundation[:, 2] = foundation_height  # Project to foundation level\n", "    \n", "    # Sample for evaluation\n", "    if len(drone_foundation_aligned) > 10000:\n", "        drone_eval = drone_foundation_aligned[np.random.choice(len(drone_foundation_aligned), 10000, replace=False)]\n", "    else:\n", "        drone_eval = drone_foundation_aligned\n", "    \n", "    if len(ifc_at_foundation) > 10000:\n", "        ifc_eval = ifc_at_foundation[np.random.choice(len(ifc_at_foundation), 10000, replace=False)]\n", "    else:\n", "        ifc_eval = ifc_at_foundation\n", "    \n", "    if len(drone_eval) > 0 and len(ifc_eval) > 0:\n", "        from scipy.spatial import cKDTree\n", "        tree = cKDTree(ifc_eval)\n", "        distances, _ = tree.query(drone_eval)\n", "        \n", "        foundation_rmse = np.sqrt(np.mean(distances**2))\n", "        foundation_good_2m = np.sum(distances < 2.0) / len(distances) * 100\n", "        foundation_good_5m = np.sum(distances < 5.0) / len(distances) * 100\n", "        \n", "        print(f\"Foundation alignment RMSE: {foundation_rmse:.2f}m\")\n", "        print(f\"Foundation good points (<2m): {foundation_good_2m:.1f}%\")\n", "        print(f\"Foundation good points (<5m): {foundation_good_5m:.1f}%\")\n", "    else:\n", "        foundation_rmse = float('inf')\n", "        foundation_good_2m = 0\n", "        foundation_good_5m = 0\n", "    \n", "    # Evaluation 2: Building footprint overlap\n", "    print(f\"\\n2. BUILDING FOOTPRINT OVERLAP:\")\n", "    \n", "    # Calculate 2D overlap using building footprints\n", "    drone_xy = drone_transformed[:, :2]\n", "    ifc_xy = ifc_points[:, :2]\n", "    \n", "    # Create 2D bounding boxes\n", "    drone_bbox = {\n", "        'x_min': np.min(drone_xy[:, 0]), 'x_max': np.max(drone_xy[:, 0]),\n", "        'y_min': np.min(drone_xy[:, 1]), 'y_max': np.max(drone_xy[:, 1])\n", "    }\n", "    \n", "    ifc_bbox = {\n", "        'x_min': np.min(ifc_xy[:, 0]), 'x_max': np.max(ifc_xy[:, 0]),\n", "        'y_min': np.min(ifc_xy[:, 1]), 'y_max': np.max(ifc_xy[:, 1])\n", "    }\n", "    \n", "    # Calculate overlap\n", "    x_overlap = max(0, min(drone_bbox['x_max'], ifc_bbox['x_max']) - \n", "                       max(drone_bbox['x_min'], ifc_bbox['x_min']))\n", "    y_overlap = max(0, min(drone_bbox['y_max'], ifc_bbox['y_max']) - \n", "                       max(drone_bbox['y_min'], ifc_bbox['y_min']))\n", "    \n", "    overlap_area = x_overlap * y_overlap\n", "    drone_area = (drone_bbox['x_max'] - drone_bbox['x_min']) * (drone_bbox['y_max'] - drone_bbox['y_min'])\n", "    ifc_area = (ifc_bbox['x_max'] - ifc_bbox['x_min']) * (ifc_bbox['y_max'] - ifc_bbox['y_min'])\n", "    \n", "    overlap_pct = (overlap_area / min(drone_area, ifc_area) * 100) if min(drone_area, ifc_area) > 0 else 0\n", "    \n", "    print(f\"2D footprint overlap: {overlap_area:.0f} m² ({overlap_pct:.1f}% of smaller area)\")\n", "    \n", "    # Overall assessment\n", "    print(f\"\\n3. OVERALL ASSESSMENT:\")\n", "    \n", "    if foundation_rmse < 5.0 and foundation_good_5m > 50:\n", "        assessment = \"EXCELLENT - Foundation alignment successful\"\n", "        success = True\n", "    elif foundation_rmse < 10.0 and foundation_good_5m > 30:\n", "        assessment = \"GOOD - Reasonable foundation alignment\"\n", "        success = True\n", "    elif overlap_pct > 80:\n", "        assessment = \"MODERATE - Good spatial overlap, foundation needs work\"\n", "        success = False\n", "    else:\n", "        assessment = \"POOR - Fundamental alignment issues\"\n", "        success = False\n", "    \n", "    print(f\"Assessment: {assessment}\")\n", "    \n", "    return {\n", "        'foundation_rmse': foundation_rmse,\n", "        'foundation_good_2m': foundation_good_2m,\n", "        'foundation_good_5m': foundation_good_5m,\n", "        'footprint_overlap_pct': overlap_pct,\n", "        'assessment': assessment,\n", "        'success': success\n", "    }\n", "\n", "# Run the complete phase-aware analysis\n", "print(\"Creating phase mismatch visualization...\")\n", "fig = visualize_phase_mismatch(drone_points, ifc_points)\n", "\n", "print(\"\\nTesting phase-aware alignment strategy...\")\n", "transformation, icp_result, foundation_height = create_alignment_strategy_for_phases(drone_points, ifc_points)\n", "\n", "print(\"\\nEvaluating phase-aware alignment...\")\n", "phase_results = evaluate_phase_aware_alignment(drone_points, ifc_points, transformation, foundation_height)\n", "\n", "print(f\"\\n{'='*80}\")\n", "print(\"FINAL PHASE-AWARE RESULTS\")\n", "print(f\"{'='*80}\")\n", "\n", "print(f\"Foundation alignment RMSE: {phase_results['foundation_rmse']:.2f}m\")\n", "print(f\"Footprint overlap: {phase_results['footprint_overlap_pct']:.1f}%\")\n", "print(f\"Assessment: {phase_results['assessment']}\")\n", "\n", "if phase_results['success']:\n", "    print(\"\\n✅ SUCCESS: Phase-aware alignment works!\")\n", "    print(\"Key insight: Align foundation footprint, not full 3D geometry\")\n", "    \n", "    # Save the successful phase-aware alignment\n", "    phase_aware_results = {\n", "        'method': 'phase_aware_foundation_footprint',\n", "        'problem_identified': 'IFC superstructure vs drone foundation phase mismatch',\n", "        'solution': 'Project IFC footprint to foundation level for alignment',\n", "        'foundation_height': foundation_height,\n", "        'transformation_matrix': transformation.tolist(),\n", "        'results': phase_results,\n", "        'success': True\n", "    }\n", "    \n", "    import json\n", "    with open(output_path / f\"{site_name}_phase_aware_alignment.json\", 'w') as f:\n", "        json.dump(phase_aware_results, f, indent=2)\n", "    \n", "    print(f\"Phase-aware results saved to: {output_path}\")\n", "    \n", "else:\n", "    print(\"\\n⚠ PARTIAL SUCCESS: Better understanding achieved\")\n", "    print(\"Recommendation: Use this analysis to guide manual control point selection\")\n", "\n", "print(f\"\\n{'='*80}\")\n", "print(\"KEY INSIGHTS FOR YOUR PROJECT\")\n", "print(f\"{'='*80}\")\n", "print(\"1. ✅ PROBLEM SOLVED: You're comparing different construction phases\")\n", "print(\"2. ✅ APPROACH: Focus on 2D footprint alignment, not 3D geometry\") \n", "print(\"3. ✅ METHOD: Project IFC building footprint to foundation level\")\n", "print(\"4. ⚠ REALITY: Perfect 3D alignment isn't possible between these datasets\")\n", "print(\"5. 🎯 USE CASE: Use this alignment for site planning, not detailed geometry comparison\")\n", "\n", "print(f\"\\nThis explains why your original RMSE was 28.92m - you were trying to align\")\n", "print(f\"underground foundations with above-ground buildings! The phase-aware approach\")\n", "print(f\"recognizes this fundamental difference and aligns what CAN be aligned.\")"]}, {"cell_type": "code", "execution_count": null, "id": "1924be34", "metadata": {}, "outputs": [], "source": ["# FINAL SOLUTION: Manual Control Point Strategy\n", "# Based on the analysis, automatic alignment isn't feasible\n", "# Need manual intervention with specific guidance\n", "\n", "def generate_control_point_strategy(drone_points, ifc_points):\n", "    \"\"\"Generate specific guidance for manual control point identification\"\"\"\n", "    \n", "    print(\"=== MANUAL CONTROL POINT STRATEGY ===\")\n", "    print(\"Based on your analysis, here's the definitive approach:\")\n", "    \n", "    # Analyze what we can identify for control points\n", "    drone_bounds = {\n", "        'x_min': np.min(drone_points[:, 0]), 'x_max': np.max(drone_points[:, 0]),\n", "        'y_min': np.min(drone_points[:, 1]), 'y_max': np.max(drone_points[:, 1]),\n", "        'z_min': np.min(drone_points[:, 2]), 'z_max': np.max(drone_points[:, 2])\n", "    }\n", "    \n", "    ifc_bounds = {\n", "        'x_min': np.min(ifc_points[:, 0]), 'x_max': np.max(ifc_points[:, 0]),\n", "        'y_min': np.min(ifc_points[:, 1]), 'y_max': np.max(ifc_points[:, 1]),\n", "        'z_min': np.min(ifc_points[:, 2]), 'z_max': np.max(ifc_points[:, 2])\n", "    }\n", "    \n", "    print(f\"\\n📊 SPATIAL ANALYSIS FOR CONTROL POINTS:\")\n", "    print(f\"Site dimensions: ~{(drone_bounds['x_max']-drone_bounds['x_min']):.0f}m x {(drone_bounds['y_max']-drone_bounds['y_min']):.0f}m\")\n", "    print(f\"Drone covers: Foundation level ({drone_bounds['z_min']:.1f} to {drone_bounds['z_max']:.1f}m)\")\n", "    print(f\"IFC covers: Building levels ({ifc_bounds['z_min']:.1f} to {ifc_bounds['z_max']:.1f}m)\")\n", "    \n", "    # Identify strategic control point locations\n", "    print(f\"\\n🎯 RECOMMENDED CONTROL POINT LOCATIONS:\")\n", "    \n", "    # Corner coordinates for large rectangular site\n", "    corners = [\n", "        (drone_bounds['x_min'], drone_bounds['y_min'], \"Southwest corner\"),\n", "        (drone_bounds['x_max'], drone_bounds['y_min'], \"Southeast corner\"), \n", "        (drone_bounds['x_max'], drone_bounds['y_max'], \"Northeast corner\"),\n", "        (drone_bounds['x_min'], drone_bounds['y_max'], \"Northwest corner\")\n", "    ]\n", "    \n", "    center_x = (drone_bounds['x_min'] + drone_bounds['x_max']) / 2\n", "    center_y = (drone_bounds['y_min'] + drone_bounds['y_max']) / 2\n", "    \n", "    control_points = [\n", "        (center_x, center_y, \"Site center\"),\n", "        (corners[0][0], corners[0][1], corners[0][2]),\n", "        (corners[2][0], corners[2][1], corners[2][2]),\n", "        (drone_bounds['x_min'], center_y, \"West midpoint\"),\n", "        (drone_bounds['x_max'], center_y, \"East midpoint\")\n", "    ]\n", "    \n", "    for i, (x, y, description) in enumerate(control_points, 1):\n", "        print(f\"   {i}. {description:15}: X={x:8.1f}, Y={y:8.1f}\")\n", "    \n", "    print(f\"\\n🔧 IMPLEMENTATION STEPS:\")\n", "    print(f\"   1. Open both datasets in CloudCompare or similar software\")\n", "    print(f\"   2. For each control point location above:\")\n", "    print(f\"      • Find the same physical feature in both datasets\")\n", "    print(f\"      • Drone: Look for foundation corners, equipment, or distinctive features\")\n", "    print(f\"      • IFC: Look for building corners or structural intersections\") \n", "    print(f\"   3. Manually measure XYZ coordinates in both datasets\")\n", "    print(f\"   4. Use these to calculate transformation matrix\")\n", "    print(f\"   5. Apply transformation and verify alignment\")\n", "    \n", "    return control_points\n", "\n", "def create_transformation_from_control_points_template():\n", "    \"\"\"Provide template code for using manual control points\"\"\"\n", "    \n", "    template_code = '''\n", "# TEMPLATE: Manual Control Point Transformation\n", "# Replace these example coordinates with your manually identified points\n", "\n", "import numpy as np\n", "from scipy.spatial.transform import Rotation\n", "\n", "def calculate_transformation_from_control_points(drone_control_points, ifc_control_points):\n", "    \"\"\"\n", "    Calculate transformation matrix from manual control points\n", "    \n", "    Args:\n", "        drone_control_points: Nx3 array of [X, Y, Z] coordinates in drone dataset\n", "        ifc_control_points: Nx3 array of [X, Y, Z] coordinates in IFC dataset\n", "    \"\"\"\n", "    \n", "    # Example control points (replace with your actual measurements)\n", "    # drone_control_points = np.array([\n", "    #     [435300.0, 5011000.0, 156.0],  # Southwest corner foundation\n", "    #     [436600.0, 5012400.0, 157.0],  # Northeast corner foundation  \n", "    #     [435950.0, 5011700.0, 156.5],  # Site center\n", "    #     [435300.0, 5011700.0, 156.2],  # West midpoint\n", "    #     [436600.0, 5011700.0, 156.8]   # East midpoint\n", "    # ])\n", "    \n", "    # ifc_control_points = np.array([\n", "    #     [435301.2, 5010999.8, 157.0],  # Southwest corner building\n", "    #     [436598.5, 5012401.2, 158.0],  # Northeast corner building\n", "    #     [435949.8, 5011700.5, 157.5],  # Building center\n", "    #     [435300.5, 5011700.2, 157.2],  # West building edge\n", "    #     [436599.2, 5011699.8, 157.8]   # East building edge\n", "    # ])\n", "    \n", "    # Center the point sets\n", "    drone_centroid = np.mean(drone_control_points, axis=0)\n", "    ifc_centroid = np.mean(ifc_control_points, axis=0)\n", "    \n", "    drone_centered = drone_control_points - drone_centroid\n", "    ifc_centered = ifc_control_points - ifc_centroid\n", "    \n", "    # Calculate optimal rotation using SVD (<PERSON><PERSON><PERSON> algorithm)\n", "    H = drone_centered.T @ ifc_centered\n", "    U, S, Vt = np.linalg.svd(H)\n", "    R = Vt.T @ U.T\n", "    \n", "    # Ensure proper rotation (det = 1)\n", "    if np.linalg.det(R) < 0:\n", "        Vt[-1, :] *= -1\n", "        R = Vt.T @ U.T\n", "    \n", "    # Calculate translation\n", "    t = ifc_centroid - R @ drone_centroid\n", "    \n", "    # Create 4x4 transformation matrix\n", "    transformation = np.eye(4)\n", "    transformation[:3, :3] = R\n", "    transformation[:3, 3] = t\n", "    \n", "    # Calculate alignment error\n", "    drone_transformed = (R @ drone_control_points.T).T + t\n", "    errors = np.linalg.norm(drone_transformed - ifc_control_points, axis=1)\n", "    rmse = np.sqrt(np.mean(errors**2))\n", "    \n", "    print(f\"Control point alignment RMSE: {rmse:.2f}m\")\n", "    print(f\"Individual errors: {errors}\")\n", "    \n", "    return transformation, rmse\n", "\n", "# Apply transformation to full drone dataset\n", "def apply_manual_transformation(drone_points, transformation):\n", "    \"\"\"Apply manually calculated transformation to full drone dataset\"\"\"\n", "    \n", "    # Apply transformation\n", "    drone_transformed = np.dot(drone_points, transformation[:3, :3].T) + transformation[:3, 3]\n", "    \n", "    return drone_transformed\n", "\n", "# Example usage:\n", "# transformation, control_rmse = calculate_transformation_from_control_points(\n", "#     drone_control_points, ifc_control_points\n", "# )\n", "# \n", "# if control_rmse < 2.0:  # Good control point alignment\n", "#     drone_aligned = apply_manual_transformation(drone_points, transformation)\n", "#     print(\"Manual alignment successful!\")\n", "# else:\n", "#     print(\"Check control point measurements - high error detected\")\n", "'''\n", "    \n", "    return template_code\n", "\n", "def final_recommendations():\n", "    \"\"\"Provide final recommendations based on complete analysis\"\"\"\n", "    \n", "    print(f\"\\n{'='*80}\")\n", "    print(\"FINAL RECOMMENDATIONS AND NEXT STEPS\")\n", "    print(f\"{'='*80}\")\n", "    \n", "    print(\"✅ ANALYSIS COMPLETE - Root cause identified:\")\n", "    print(\"   • IFC represents building superstructure (150-160m height)\")\n", "    print(\"   • Drone captured foundation/site preparation (0-20m height)\")\n", "    print(\"   • No geometric overlap for automatic alignment\")\n", "    \n", "    print(f\"\\n🎯 RECOMMENDED APPROACH:\")\n", "    print(\"   1. ACCEPT that perfect 3D alignment is impossible\")\n", "    print(\"   2. USE manual control points for spatial referencing\")\n", "    print(\"   3. FOCUS on 2D/footprint alignment for planning purposes\")\n", "    print(\"   4. UNDERSTAND this is a construction phase difference, not an error\")\n", "    \n", "    print(f\"\\n📋 IMMEDIATE ACTION PLAN:\")\n", "    print(\"   1. Export both point clouds to CloudCompare\")\n", "    print(\"   2. Identify 4-5 corresponding features (building corners, equipment)\")\n", "    print(\"   3. Manually measure coordinates in both datasets\")\n", "    print(\"   4. Use provided template code to calculate transformation\")\n", "    print(\"   5. Evaluate if 2D footprint alignment meets your needs\")\n", "    \n", "    print(f\"\\n⚠ REALISTIC EXPECTATIONS:\")\n", "    print(\"   • Best achievable RMSE: 2-5m (for footprint alignment)\")\n", "    print(\"   • 3D RMSE will remain high due to phase differences\")\n", "    print(\"   • Focus on XY alignment accuracy, accept Z differences\")\n", "    \n", "    print(f\"\\n🏗 PROJECT INSIGHTS:\")\n", "    print(\"   • Your drone scan shows excellent foundation documentation\")\n", "    print(\"   • IFC model represents the future building state\")\n", "    print(\"   • Both datasets are valuable but serve different purposes\")\n", "    print(\"   • Consider scanning again after building construction for 3D comparison\")\n", "    \n", "    print(f\"\\n📈 SUCCESS METRICS FOR MANUAL ALIGNMENT:\")\n", "    print(\"   • Control point RMSE < 2m: Excellent\")\n", "    print(\"   • Footprint overlap > 90%: Very good spatial alignment\")\n", "    print(\"   • Visual alignment of building corners: Acceptable for planning\")\n", "\n", "# Run the final analysis\n", "control_points = generate_control_point_strategy(drone_points, ifc_points)\n", "template_code = create_transformation_from_control_points_template()\n", "final_recommendations()\n", "\n", "print(f\"\\n{'='*80}\")\n", "print(\"CONTROL POINT TEMPLATE CODE\")\n", "print(f\"{'='*80}\")\n", "print(template_code)\n", "\n", "# Save final analysis results\n", "final_analysis = {\n", "    'problem_identification': {\n", "        'issue': 'Construction phase mismatch - foundation vs superstructure',\n", "        'drone_content': 'Foundation and site preparation (0-20m height)',\n", "        'ifc_content': 'Building superstructure (150-160m height)',\n", "        'geometric_overlap': 'None - different construction phases'\n", "    },\n", "    'automatic_alignment_results': {\n", "        'best_rmse_achieved': 28.54,\n", "        'phase_aware_rmse': 19853.51,\n", "        'conclusion': 'Automatic alignment not feasible'\n", "    },\n", "    'recommended_solution': {\n", "        'method': 'Manual control point identification',\n", "        'expected_accuracy': '2-5m RMSE for footprint alignment',\n", "        'tools_needed': 'CloudCompare or similar point cloud software',\n", "        'control_points_needed': 4-5\n", "    },\n", "    'control_point_locations': [\n", "        {'name': cp[2], 'approximate_x': cp[0], 'approximate_y': cp[1]} \n", "        for cp in control_points\n", "    ],\n", "    'success_criteria': {\n", "        'control_point_rmse': '<2m',\n", "        'footprint_overlap': '>90%',\n", "        'practical_use': 'Site planning and spatial referencing'\n", "    }\n", "}\n", "\n", "import json\n", "with open(output_path / f\"{site_name}_final_analysis_complete.json\", 'w') as f:\n", "    json.dump(final_analysis, f, indent=2)\n", "\n", "print(f\"\\n✅ Complete analysis saved to: {output_path}\")\n", "print(f\"📁 Files created:\")\n", "print(f\"   • {site_name}_final_analysis_complete.json\")\n", "print(f\"   • Use the template code above for manual control point alignment\")\n", "\n", "print(f\"\\n🎉 MISSION ACCOMPLISHED!\")\n", "print(f\"You now understand exactly why the alignment failed and how to fix it.\")\n", "print(f\"This is actually a very common issue in construction projects - you're not alone!\")"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}