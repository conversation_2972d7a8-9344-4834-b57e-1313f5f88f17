{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# ICP Point Cloud Alignment\n", "\n", "Streamlined ICP alignment between drone scan point clouds and IFC point clouds.\n", "\n", "**Steps:**\n", "1. Load Data (Drone scan & IFC point cloud)\n", "2. Preprocess (measure and do Z-shift correction)\n", "3. <PERSON> (Coarse-to-fine alignment)\n", "4. Visualize Results (Before/after alignment)\n", "5. <PERSON><PERSON><PERSON> (RMSE, max deviation)\n", "\n", "**Key Principle:** Drone point cloud represents ground truth elevation (Z), IFC model is aligned to it.\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Parameters (Papermill)\n", "ground_method = \"ransac_pmf\"  # Ground segmentation method: csf, pmf, ransac\n", "site_name = \"trino_enel\"\n", "\n", "icp_max_iterations = 50\n", "icp_tolerance = 1e-6\n", "voxel_size = 0.02  # For downsampling if needed\n", "\n", "# Hybrid parameters\n", "xy_refinement_threshold = 10.0  # Only accept small XY refinements\n", "max_correspondence_distance = 2.0  # Conservative for 2D ICP\n", "\n", "STANDARD_SAMPLE_SIZE = 50000\n", "RANDOM_SEED = 42              \n", "\n", "output_dir = \"../../../data/output_runs/icp_alignment\"\n", "enable_visualization = True\n", "save_results = True"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== HYBRID XY-COORDINATE ALIGNMENT ===\n", "Strategy: Coordinate-only Z + Selective XY refinement\n", "Site: trino_enel\n", "Output: ../../../data/output_runs/icp_alignment/ransac_pmf\n", "Timestamp: 2025-07-17 18:10:27\n"]}], "source": ["# Imports\n", "import numpy as np\n", "import open3d as o3d\n", "import matplotlib.pyplot as plt\n", "import pandas as pd\n", "import time\n", "import json\n", "from pathlib import Path\n", "from datetime import datetime\n", "from scipy.spatial import cKDTree\n", "\n", "# Setup\n", "np.random.seed(42)\n", "output_path = Path(output_dir) / ground_method\n", "output_path.mkdir(parents=True, exist_ok=True)\n", "\n", "print(\"=== HYBRID XY-COORDINATE ALIGNMENT ===\")\n", "print(f\"Strategy: Coordinate-only Z + Selective XY refinement\")\n", "print(f\"Site: {site_name}\")\n", "print(f\"Output: {output_path}\")\n", "print(f\"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Load Data (Drone scan & IFC point cloud)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading data with corrected approach...\n", "Drone file: ../../../data/processed/trino_enel/ground_segmentation/ransac_pmf/trino_enel_nonground.ply\n", "IFC metadata file: ../../../data/processed/trino_enel/ifc_metadata/GRE.EEC.S.00.IT.P.14353.00.265_enhanced_metadata.csv\n", "IFC point cloud file: ../../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply (for comparison)\n"]}], "source": ["# Define file paths\n", "drone_file = f\"../../../data/processed/{site_name}/ground_segmentation/{ground_method}/{site_name}_nonground.ply\"\n", "ifc_metadata_file = f\"../../../data/processed/{site_name}/ifc_metadata/GRE.EEC.S.00.IT.P.14353.00.265_enhanced_metadata.csv\"\n", "ifc_file = f\"../../../data/processed/{site_name}/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\"\n", "\n", "print(\"Loading data with corrected approach...\")\n", "print(f\"Drone file: {drone_file}\")\n", "print(f\"IFC metadata file: {ifc_metadata_file}\")\n", "print(f\"IFC point cloud file: {ifc_pointcloud_file} (for comparison)\")\n", "\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded drone scan: 508,032 points\n", "Loaded IFC model: 5,480,340 points\n", "Coordinate Analysis:\n", "  Full 3D offset: [-46.87, 20.30, 155.43]\n", "  XY component: 51.08m\n", "  Z component: 155.43m\n"]}], "source": ["def load_and_analyze_data():\n", "    # Load point clouds\n", "    drone_points = np.asarray(o3d.io.read_point_cloud(drone_file).points)\n", "    ifc_points = np.asarray(o3d.io.read_point_cloud(ifc_file).points)\n", "    \n", "    print(f\"Loaded drone scan: {len(drone_points):,} points\")\n", "    print(f\"Loaded IFC model: {len(ifc_points):,} points\")\n", "    \n", "    # Compute coordinate-only offset\n", "    drone_center = np.mean(drone_points, axis=0)\n", "    ifc_center = np.mean(ifc_points, axis=0)\n", "    full_offset = ifc_center - drone_center\n", "    \n", "    print(f\"Coordinate Analysis:\")\n", "    print(f\"  Full 3D offset: [{full_offset[0]:.2f}, {full_offset[1]:.2f}, {full_offset[2]:.2f}]\")\n", "    print(f\"  XY component: {np.linalg.norm(full_offset[:2]):.2f}m\")\n", "    print(f\"  Z component: {full_offset[2]:.2f}m\")\n", "    \n", "    return drone_points, ifc_points, full_offset\n", "\n", "drone_points, ifc_points, coordinate_offset = load_and_analyze_data()\n"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "INITIAL POINT CLOUD STATISTICS\n", "\n", "Drone scan (ground truth):\n", "  Points: 508,032\n", "  X range: [435220.57, 436795.03]\n", "  Y range: [5010811.69, 5012547.89]\n", "  Z range: [-0.58, 14.79]\n", "\n", "IFC model (to be aligned):\n", "  Points: 5,480,340\n", "  X range: [435267.17, 436719.98]\n", "  Y range: [5010900.69, 5012462.43]\n", "  Z range: [152.85, 161.66]\n"]}], "source": ["# Display initial statistics\n", "print(\"\\nINITIAL POINT CLOUD STATISTICS\")\n", "print(\"\\nDrone scan (ground truth):\")\n", "print(f\"  Points: {drone_points.shape[0]:,}\")\n", "print(f\"  X range: [{drone_points[:, 0].min():.2f}, {drone_points[:, 0].max():.2f}]\")\n", "print(f\"  Y range: [{drone_points[:, 1].min():.2f}, {drone_points[:, 1].max():.2f}]\")\n", "print(f\"  Z range: [{drone_points[:, 2].min():.2f}, {drone_points[:, 2].max():.2f}]\")\n", "\n", "print(\"\\nIFC model (to be aligned):\")\n", "print(f\"  Points: {ifc_points.shape[0]:,}\")\n", "print(f\"  X range: [{ifc_points[:, 0].min():.2f}, {ifc_points[:, 0].max():.2f}]\")\n", "print(f\"  Y range: [{ifc_points[:, 1].min():.2f}, {ifc_points[:, 1].max():.2f}]\")\n", "print(f\"  Z range: [{ifc_points[:, 2].min():.2f}, {ifc_points[:, 2].max():.2f}]\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# def offset_based_normalization(points):\n", "#     \"\"\"\n", "#     Subtracts the minimum Z-value from a point cloud to bring it to a Z=0 base frame.\n", "#     Returns the normalized points and the offset applied.\n", "#     \"\"\"\n", "#     if points.shape[0] == 0:\n", "#         print(\"Warning: Empty point cloud provided for offset-based normalization\")\n", "#         return points, 0.0\n", "    \n", "#     # Find minimum Z-value\n", "#     min_z = np.min(points[:, 2])\n", "    \n", "#     # Subtract min <PERSON> from all points\n", "#     normalized_points = points.copy()\n", "#     normalized_points[:, 2] -= min_z\n", "    \n", "#     return normalized_points, min_z\n", "\n", "# drone_points, drone_z_offset = offset_based_normalization(drone_points)\n", "# ifc_points, ifc_z_offset = offset_based_normalization(ifc_points)\n", "\n", "# print(f\"Drone Z-offset applied: {drone_z_offset:.4f}m\")\n", "# print(f\"IFC Z-offset applied: {ifc_z_offset:.4f}m\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# # Display initial statistics\n", "# print(\"\\nDrone scan (ground truth):\")\n", "# print(f\"  Points: {drone_points.shape[0]:,}\")\n", "# print(f\"  X range: [{drone_points[:, 0].min():.2f}, {drone_points[:, 0].max():.2f}]\")\n", "# print(f\"  Y range: [{drone_points[:, 1].min():.2f}, {drone_points[:, 1].max():.2f}]\")\n", "# print(f\"  Z range: [{drone_points[:, 2].min():.2f}, {drone_points[:, 2].max():.2f}]\")\n", "\n", "# print(\"\\nIFC model (to be aligned):\")\n", "# print(f\"  Points: {ifc_points.shape[0]:,}\")\n", "# print(f\"  X range: [{ifc_points[:, 0].min():.2f}, {ifc_points[:, 0].max():.2f}]\")\n", "# print(f\"  Y range: [{ifc_points[:, 1].min():.2f}, {ifc_points[:, 1].max():.2f}]\")\n", "# print(f\"  Z range: [{ifc_points[:, 2].min():.2f}, {ifc_points[:, 2].max():.2f}]\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Apply Coordinate-Only Alignment"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Coordinate Alignment Results:\n", "  Residual centroid error: 0.000000m\n", "  Status: PERFECT coordinate alignment achieved\n"]}], "source": ["def apply_coordinate_alignment(drone_pts, offset):\n", "    # Apply full coordinate offset\n", "    drone_aligned = drone_pts + offset\n", "    \n", "    # Verify perfect coordinate alignment\n", "    aligned_center = np.mean(drone_aligned, axis=0)\n", "    ifc_center = np.mean(ifc_points, axis=0)\n", "    residual_error = np.linalg.norm(aligned_center - ifc_center)\n", "    \n", "    print(f\"Coordinate Alignment Results:\")\n", "    print(f\"  Residual centroid error: {residual_error:.6f}m\")\n", "    print(f\"  Status: PERFECT coordinate alignment achieved\")\n", "    \n", "    return drone_aligned\n", "\n", "# def apply_coordinate_alignment(drone_pts, ifc_pts):\n", "#     \"\"\"\n", "#     Aligns drone and IFC point clouds by matching XY centroids, preserving Z=0 from normalization.\n", "#     Returns the aligned drone points and the XY offset applied.\n", "#     \"\"\"\n", "#     # Calculate XY centroids\n", "#     drone_center = np.mean(drone_pts[:, :2], axis=0)\n", "#     ifc_center = np.mean(ifc_pts[:, :2], axis=0)\n", "    \n", "#     # Compute XY offset\n", "#     xy_offset = ifc_center - drone_center\n", "    \n", "#     # Apply XY offset, preserve Z\n", "#     drone_aligned = drone_pts.copy()\n", "#     drone_aligned[:, :2] += xy_offset\n", "    \n", "#     # Verify alignment\n", "#     aligned_center = np.mean(drone_aligned[:, :2], axis=0)\n", "#     residual_error = np.linalg.norm(aligned_center - ifc_center)\n", "    \n", "#     print(f\"Coordinate Alignment Results:\")\n", "#     print(f\"  XY offset applied: [{xy_offset[0]:.3f}, {xy_offset[1]:.3f}]m\")\n", "#     print(f\"  Residual XY centroid error: {residual_error:.6f}m\")\n", "#     print(f\"  Status: XY centroid alignment achieved, Z preserved at 0\")\n", "    \n", "#     return drone_aligned, xy_offset\n", "\n", "drone_coordinate_aligned = apply_coordinate_alignment(drone_points, coordinate_offset)\n", "# drone_coordinate_aligned, coordinate_offset = apply_coordinate_alignment(drone_points, ifc_points)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Prepare for XY-Only ICP Test"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["XY-only point clouds prepared:\n", "  Source (Drone XY): 490,754 points\n", "  Target (IFC XY): 25,467 points\n"]}], "source": ["def prepare_xy_icp_test(drone_aligned, ifc_pts):\n", "    # Extract XY coordinates only (set Z=0 for 2D ICP)\n", "    drone_xy = drone_aligned[:, :2]\n", "    ifc_xy = ifc_pts[:, :2]\n", "    \n", "    # Create 2D point clouds (Z=0)\n", "    drone_xy_3d = np.column_stack([drone_xy, np.zeros(len(drone_xy))])\n", "    ifc_xy_3d = np.column_stack([ifc_xy, np.zeros(len(ifc_xy))])\n", "    \n", "    # Create Open3D point clouds\n", "    source_pcd = o3d.geometry.PointCloud()\n", "    source_pcd.points = o3d.utility.Vector3dVector(drone_xy_3d)\n", "    \n", "    target_pcd = o3d.geometry.PointCloud()\n", "    target_pcd.points = o3d.utility.Vector3dVector(ifc_xy_3d)\n", "    \n", "    # Downsample for performance\n", "    source_pcd = source_pcd.voxel_down_sample(0.15)\n", "    target_pcd = target_pcd.voxel_down_sample(0.15)\n", "    \n", "    # Remove outliers\n", "    source_pcd, _ = source_pcd.remove_statistical_outlier(nb_neighbors=20, std_ratio=2.0)\n", "    target_pcd, _ = target_pcd.remove_statistical_outlier(nb_neighbors=20, std_ratio=2.0)\n", "    \n", "    print(f\"XY-only point clouds prepared:\")\n", "    print(f\"  Source (Drone XY): {len(source_pcd.points):,} points\")\n", "    print(f\"  Target (IFC XY): {len(target_pcd.points):,} points\")\n", "    \n", "    return source_pcd, target_pcd\n", "\n", "source_xy_pcd, target_xy_pcd = prepare_xy_icp_test(drone_coordinate_aligned, ifc_points)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Calculate XY Offset After Downsampling\n"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["XY Offset Analysis:\n", "  Source center: [435984.956, 5011749.364]\n", "  Target center: [435992.292, 5011770.790]\n", "  XY offset: [7.337, 21.426]\n", "  XY offset magnitude: 22.647m\n"]}], "source": ["def calculate_xy_offset(source_pcd, target_pcd):\n", "    source_center = np.mean(np.asarray(source_pcd.points), axis=0)\n", "    target_center = np.mean(np.asarray(target_pcd.points), axis=0)\n", "    \n", "    xy_offset = target_center[:2] - source_center[:2]  # Only XY components\n", "    xy_offset_magnitude = np.linalg.norm(xy_offset)\n", "    \n", "    print(f\"XY Offset Analysis:\")\n", "    print(f\"  Source center: [{source_center[0]:.3f}, {source_center[1]:.3f}]\")\n", "    print(f\"  Target center: [{target_center[0]:.3f}, {target_center[1]:.3f}]\")\n", "    print(f\"  XY offset: [{xy_offset[0]:.3f}, {xy_offset[1]:.3f}]\")\n", "    print(f\"  XY offset magnitude: {xy_offset_magnitude:.3f}m\")\n", "    \n", "    return xy_offset, xy_offset_magnitude\n", "\n", "xy_offset, xy_offset_magnitude = calculate_xy_offset(source_xy_pcd, target_xy_pcd)\n"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"image/png": "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******************************************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", "text/plain": ["<Figure size 1500x500 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# See what you're trying to align:\n", "import matplotlib.pyplot as plt\n", "from mpl_toolkits.mplot3d import Axes3D\n", "import numpy as np\n", "\n", "# Sample points for visualization\n", "drone_sample = np.asarray(source_xy_pcd.points)[::100]  # Every 100th point\n", "ifc_sample = np.asarray(target_xy_pcd.points)[::50]     # Every 50th point\n", "\n", "fig = plt.figure(figsize=(15, 5))\n", "\n", "# Side by side view\n", "ax1 = fig.add_subplot(131, projection='3d')\n", "ax1.scatter(drone_sample[:, 0], drone_sample[:, 1], drone_sample[:, 2], \n", "           c='red', s=1, alpha=0.6, label='Drone')\n", "ax1.set_title('Drone Points')\n", "\n", "ax2 = fig.add_subplot(132, projection='3d')\n", "ax2.scatter(ifc_sample[:, 0], ifc_sample[:, 1], ifc_sample[:, 2], \n", "           c='blue', s=1, alpha=0.6, label='IFC')\n", "ax2.set_title('IFC Points')\n", "\n", "# Overlay view\n", "ax3 = fig.add_subplot(133, projection='3d')\n", "ax3.scatter(drone_sample[:, 0], drone_sample[:, 1], drone_sample[:, 2], \n", "           c='red', s=1, alpha=0.4, label='Drone')\n", "ax3.scatter(ifc_sample[:, 0], ifc_sample[:, 1], ifc_sample[:, 2], \n", "           c='blue', s=1, alpha=0.4, label='IFC')\n", "ax3.set_title('Overlay')\n", "ax3.legend()\n", "\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Test XY-Only ICP Refinement\n"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== TESTING XY-ONLY ICP REFINEMENT ===\n", "XY ICP Results:\n", "  Fitness: 0.098277\n", "  RMSE: 1.396551\n", "  XY refinement: [4790.030, -414.825]\n", "  Refinement magnitude: 4807.959m\n"]}], "source": ["def test_xy_icp_refinement(source_pcd, target_pcd):\n", "    print(\"=== TESTING XY-ONLY ICP REFINEMENT ===\")\n", "    \n", "    # Run 2D ICP (point-to-point for 2D data)\n", "    icp_result = o3d.pipelines.registration.registration_icp(\n", "        source_pcd, target_pcd,\n", "        max_correspondence_distance=max_correspondence_distance,\n", "        init=np.eye(4),  # Start from aligned position\n", "        estimation_method=o3d.pipelines.registration.TransformationEstimationPointToPoint(),\n", "        criteria=o3d.pipelines.registration.ICPConvergenceCriteria(max_iteration=50)\n", "    )\n", "    \n", "    # Extract XY refinement (ignore any Z movement)\n", "    xy_refinement = icp_result.transformation[:2, 3]\n", "    xy_refinement_magnitude = np.linalg.norm(xy_refinement)\n", "    \n", "    print(f\"XY ICP Results:\")\n", "    print(f\"  Fitness: {icp_result.fitness:.6f}\")\n", "    print(f\"  RMSE: {icp_result.inlier_rmse:.6f}\")\n", "    print(f\"  XY refinement: [{xy_refinement[0]:.3f}, {xy_refinement[1]:.3f}]\")\n", "    print(f\"  Refinement magnitude: {xy_refinement_magnitude:.3f}m\")\n", "    \n", "    return xy_refinement, xy_refinement_magnitude, icp_result.fitness\n", "\n", "xy_refinement, xy_refinement_mag, xy_fitness = test_xy_icp_refinement(source_xy_pcd, target_xy_pcd)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Hybrid Decision Logic\n"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== HYBRID ALIGNMENT DECISION ===\n", "REJECTING XY refinement: 4807.959m > 10.0m threshold\n", "Using coordinate-only alignment (more reliable)\n"]}], "source": ["def apply_hybrid_alignment(drone_aligned, xy_refinement, xy_magnitude):\n", "    print(\"=== HYBRID ALIGNMENT DECISION ===\")\n", "    \n", "    if xy_magnitude < xy_refinement_threshold:\n", "        print(f\"ACCEPTING XY refinement: {xy_magnitude:.3f}m < {xy_refinement_threshold}m threshold\")\n", "        \n", "        # Apply XY refinement while preserving Z from coordinate alignment\n", "        drone_hybrid = drone_aligned.copy()\n", "        drone_hybrid[:, 0] += xy_refinement[0]  # Refine X\n", "        drone_hybrid[:, 1] += xy_refinement[1]  # Refine Y\n", "        # Z stays from coordinate-only alignment\n", "        \n", "        method_used = \"hybrid_coordinate_xy\"\n", "        print(f\"Applied hybrid transformation: XY refinement + coordinate Z\")\n", "        \n", "    else:\n", "        print(f\"REJECTING XY refinement: {xy_magnitude:.3f}m > {xy_refinement_threshold}m threshold\")\n", "        print(f\"Using coordinate-only alignment (more reliable)\")\n", "        \n", "        drone_hybrid = drone_aligned.copy()\n", "        method_used = \"coordinate_only\"\n", "    \n", "    return drone_hybrid, method_used\n", "\n", "drone_final, alignment_method = apply_hybrid_alignment(drone_coordinate_aligned, xy_refinement, xy_refinement_mag)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Evaluate Final Alignment Quality\n"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== FINAL ALIGNMENT QUALITY ===\n", "Method used: coordinate_only\n", "Final centroid separation: 0.000000m\n", "RMSE: 28.55m\n", "Median distance: 4.92m\n", "Good points (<2m): 7.6%\n"]}], "source": ["def evaluate_final_alignment(drone_final, ifc_pts, method):\n", "    # Calculate final centroid separation\n", "    final_drone_center = np.mean(drone_final, axis=0)\n", "    final_ifc_center = np.mean(ifc_pts, axis=0)\n", "    final_separation = np.linalg.norm(final_drone_center - final_ifc_center)\n", "    \n", "    # Quality assessment with sampling\n", "    # sample_size = 5000\n", "    # drone_sample = drone_final[np.random.choice(len(drone_final), min(sample_size, len(drone_final)), replace=False)]\n", "    # ifc_sample = ifc_pts[np.random.choice(len(ifc_pts), min(sample_size, len(ifc_pts)), replace=False)]\n", "    \n", "    np.random.seed(RANDOM_SEED)  # Ensure reproducible sampling\n", "    \n", "    # Use same sample size across all methods\n", "    n_drone = min(STANDARD_SAMPLE_SIZE, len(drone_final))\n", "    n_ifc = min(STANDARD_SAMPLE_SIZE, len(ifc_pts))\n", "    \n", "    # Fixed sampling indices for reproducibility\n", "    drone_indices = np.random.choice(len(drone_final), n_drone, replace=False)\n", "    ifc_indices = np.random.choice(len(ifc_pts), n_ifc, replace=False)\n", "\n", "    drone_sample = drone_final[drone_indices]\n", "    ifc_sample = ifc_pts[ifc_indices]\n", "\n", "    # Nearest neighbor distances\n", "    tree = cKDTree(ifc_sample)\n", "    distances, _ = tree.query(drone_sample)\n", "    \n", "    # Calculate metrics\n", "    rmse = np.sqrt(np.mean(distances**2))\n", "    median_dist = np.median(distances)\n", "    good_pct = np.sum(distances < 2.0) / len(distances) * 100\n", "    \n", "    print(f\"=== FINAL ALIGNMENT QUALITY ===\")\n", "    print(f\"Method used: {method}\")\n", "    print(f\"Final centroid separation: {final_separation:.6f}m\")\n", "    print(f\"RMSE: {rmse:.2f}m\")\n", "    print(f\"Median distance: {median_dist:.2f}m\")\n", "    print(f\"Good points (<2m): {good_pct:.1f}%\")\n", "    \n", "    return {\n", "        'method': method,\n", "        'final_separation': final_separation,\n", "        'rmse': rmse,\n", "        'median_distance': median_dist,\n", "        'good_pct': good_pct,\n", "        'xy_refinement_applied': method == \"hybrid_coordinate_xy\",\n", "        'xy_refinement_magnitude': xy_refinement_mag if method == \"hybrid_coordinate_xy\" else 0.0\n", "    }\n", "\n", "final_results = evaluate_final_alignment(drone_final, ifc_points, alignment_method)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 8. Compare Approaches"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== APPROACH COMPARISON ===\n", "Coordinate-only centroid error: 0.000m\n", "Hybrid approach centroid error: 0.000000m\n", "Hybrid approach RMSE: 28.55m\n", "XY refinement of 4807.959m was rejected (too large)\n", "Assessment: COORDINATE-ONLY PREFERRED\n"]}], "source": ["def compare_alignment_approaches():\n", "    print(\"=== APPROACH COMPARISON ===\")\n", "    \n", "    # Coordinate-only (perfect centroid alignment by definition)\n", "    coord_only_error = 0.000\n", "    \n", "    # Current results\n", "    current_rmse = final_results['rmse']\n", "    current_separation = final_results['final_separation']\n", "    \n", "    print(f\"Coordinate-only centroid error: {coord_only_error:.3f}m\")\n", "    print(f\"Hybrid approach centroid error: {current_separation:.6f}m\")\n", "    print(f\"Hybrid approach RMSE: {current_rmse:.2f}m\")\n", "    \n", "    if final_results['xy_refinement_applied']:\n", "        print(f\"XY refinement of {xy_refinement_mag:.3f}m was applied\")\n", "        if current_separation < 0.01:\n", "            assessment = \"SUCCESSFUL REFINEMENT\"\n", "        else:\n", "            assessment = \"REFINEMENT WITH TRADE-OFFS\"\n", "    else:\n", "        print(f\"XY refinement of {xy_refinement_mag:.3f}m was rejected (too large)\")\n", "        assessment = \"COORDINATE-ONLY PREFERRED\"\n", "    \n", "    print(f\"Assessment: {assessment}\")\n", "    \n", "    return assessment\n", "\n", "comparison_result = compare_alignment_approaches()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. Save Results"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Results saved to: ../../../data/output_runs/icp_alignment/ransac_pmf\n", "=== HYBRID XY-COORDINATE ALIGNMENT COMPLETE ===\n", "Final method: coordinate_only\n", "Final RMSE: 28.55m\n", "Assessment: COORDINATE-ONLY PREFERRED\n"]}], "source": ["if save_results:\n", "    output_path = Path(output_dir) / ground_method\n", "    output_path.mkdir(parents=True, exist_ok=True)\n", "    \n", "    # Save final aligned point cloud\n", "    final_pcd = o3d.geometry.PointCloud()\n", "    final_pcd.points = o3d.utility.Vector3dVector(drone_final)\n", "    aligned_file = output_path / f\"{site_name}_hybrid_aligned.ply\"\n", "    o3d.io.write_point_cloud(str(aligned_file), final_pcd)\n", "    \n", "    # Save comprehensive results\n", "    results_data = {\n", "        'coordinate_offset': coordinate_offset.tolist(),\n", "        'xy_offset_after_downsampling': xy_offset.tolist(),\n", "        'xy_refinement_proposed': xy_refinement.tolist(),\n", "        'xy_refinement_magnitude': xy_refinement_mag,\n", "        'xy_fitness': xy_fitness,\n", "        'refinement_threshold': xy_refinement_threshold,\n", "        'final_results': final_results,\n", "        'comparison_assessment': comparison_result,\n", "        'decision_logic': {\n", "            'xy_magnitude_vs_threshold': f\"{xy_refinement_mag:.3f}m vs {xy_refinement_threshold}m\",\n", "            'decision': \"ACCEPT\" if final_results['xy_refinement_applied'] else \"REJECT\",\n", "            'reason': \"Small refinement accepted\" if final_results['xy_refinement_applied'] \n", "                     else \"Large refinement rejected - coordinate-only more reliable\"\n", "        }\n", "    }\n", "    \n", "    with open(output_path / f\"{site_name}_hybrid_alignment_results.json\", 'w') as f:\n", "        json.dump(results_data, f, indent=2)\n", "    \n", "    print(f\"Results saved to: {output_path}\")\n", "\n", "print(\"=== HYBRID XY-COORDINATE ALIGNMENT COMPLETE ===\")\n", "print(f\"Final method: {alignment_method}\")\n", "print(f\"Final RMSE: {final_results['rmse']:.2f}m\")\n", "print(f\"Assessment: {comparison_result}\")\n"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== STANDARDIZED EVALUATION: HYBRID_XY_COORDINATE ===\n", "RMSE: 28.92m\n", "Sample size: 20,000 points\n", "{'method': 'hybrid_xy_coordinate', 'rmse': 28.924644935890676, 'median_distance': 5.943021742098294, 'sample_size': 20000, 'evaluation': 'standardized_fair_comparison'}\n"]}], "source": ["def fair_comparison_evaluation(drone_aligned, ifc_pts, method_name):\n", "    \"\"\"Fair comparison with identical evaluation methodology\"\"\"\n", "    \n", "    # Use large consistent sample\n", "    SAMPLE_SIZE = 20000\n", "    np.random.seed(42)  # Fixed seed\n", "    \n", "    # Consistent sampling\n", "    drone_sample = drone_aligned[np.random.choice(len(drone_aligned), \n", "                                                 min(SAMPLE_SIZE, len(drone_aligned)), \n", "                                                 replace=False)]\n", "    ifc_sample = ifc_pts[np.random.choice(len(ifc_pts), \n", "                                         min(SAMPLE_SIZE, len(ifc_pts)), \n", "                                         replace=False)]\n", "    \n", "    # Standard evaluation\n", "    tree = cKDTree(ifc_sample)\n", "    distances, _ = tree.query(drone_sample)\n", "    \n", "    results = {\n", "        'method': method_name,\n", "        'rmse': np.sqrt(np.mean(distances**2)),\n", "        'median_distance': np.median(distances),\n", "        'sample_size': len(drone_sample),\n", "        'evaluation': 'standardized_fair_comparison'\n", "    }\n", "    \n", "    print(f\"=== STANDARDIZED EVALUATION: {method_name.upper()} ===\")\n", "    print(f\"RMSE: {results['rmse']:.2f}m\")\n", "    print(f\"Sample size: {results['sample_size']:,} points\")\n", "    \n", "    return results\n", "\n", "fair_comparison_results = fair_comparison_evaluation(drone_final, ifc_points, \"hybrid_xy_coordinate\")\n", "print(fair_comparison_results)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}